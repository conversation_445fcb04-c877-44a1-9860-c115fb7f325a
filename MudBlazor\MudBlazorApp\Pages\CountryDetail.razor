@page "/country/{id:int}"
@inherits CountryDetailBase
@using MudBlazorApp.Models

<MudPaper Class="pa-4" Style="max-width: 500px;">
    <MudCard>
        <MudCardHeader>
            <CardHeaderContent>
                <MudText Typo="Typo.h5">Country Details</MudText>
            </CardHeaderContent>
        </MudCardHeader>
        <MudCardContent>
            @if (Country == null && IsLoading)
            {
                <MudProgressLinear Color="Color.Primary" Indeterminate="true" Class="my-7" />
                <MudText Align="Align.Center">Loading details...</MudText>
            }
            else if (Country != null)
            {
                <MudSimpleTable>
                    <tbody>
                        <tr>
                            <td><MudText Typo="Typo.body1"><strong>ID</strong></MudText></td>
                            <td>@Country.Id</td>
                        </tr>
                        <tr>
                            <td><MudText Typo="Typo.body1"><strong>Name</strong></MudText></td>
                            <td>@Country.Name</td>
                        </tr>
                        <tr>
                            <td><MudText Typo="Typo.body1"><strong>Verified</strong></MudText></td>
                            <td>@(Country.Verified ? "Yes" : "No")</td>
                        </tr>
                        <tr>
                            <td><MudText Typo="Typo.body1"><strong>Mode</strong></MudText></td>
                            <td>@Country.Mode</td>
                        </tr>
                        <tr>
                            <td><MudText Typo="Typo.body1"><strong>Created On</strong></MudText></td>
                            <td>@Country.CreatedOn.ToShortDateString()</td>
                        </tr>
                        <tr>
                            <td><MudText Typo="Typo.body1"><strong>Last Modified</strong></MudText></td>
                            <td>@Country.LastModified.ToShortDateString()</td>
                        </tr>
                    </tbody>
                </MudSimpleTable>
            }
            else
            {
                <MudAlert Severity="Severity.Error">@ErrorMessage</MudAlert>
            }
        </MudCardContent>
        <MudCardActions>
            <MudButton Variant="Variant.Filled" Color="Color.Primary" OnClick="BackToList">Back to List</MudButton>
        </MudCardActions>
    </MudCard>
</MudPaper>
