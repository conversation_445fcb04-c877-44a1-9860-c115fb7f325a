(function () {
    'use strict';

    angular
        .module('mvcAngApp')
        .controller('TypeAheadController', TypeAheadController);

    TypeAheadController.$inject = ['TypeAheadService'];

    function TypeAheadController(TypeAheadService) {
        var vm = this;

        // Properties
        vm.selectedCountry = '';
        vm.selectedCity = '';
        vm.selectedProduct = '';
        vm.selectedUser = '';
        
        vm.countryConfig = null;
        vm.cityConfig = null;
        vm.productConfig = null;
        vm.userConfig = null;
        
        vm.dataTypes = [];
        vm.selectedItems = [];

        // Methods
        vm.onCountrySelect = onCountrySelect;
        vm.onCitySelect = onCitySelect;
        vm.onProductSelect = onProductSelect;
        vm.onUserSelect = onUserSelect;
        vm.clearSelections = clearSelections;
        vm.getDataTypeLabel = getDataTypeLabel;

        // Initialize
        activate();

        function activate() {
            console.log('TypeAheadController activated');
            
            // Get available data types
            vm.dataTypes = TypeAheadService.getDataTypes();
            
            // Create configurations for different type-ahead instances
            vm.countryConfig = TypeAheadService.createConfig({
                dataType: 'countries',
                placeholder: 'Search for countries...',
                minLength: 1,
                debounceMs: 300,
                showDropdownOnFocus: true
            });

            vm.cityConfig = TypeAheadService.createConfig({
                dataType: 'cities',
                placeholder: 'Search for cities...',
                minLength: 2,
                debounceMs: 400
            });

            vm.productConfig = TypeAheadService.createConfig({
                dataType: 'products',
                placeholder: 'Search for products...',
                minLength: 1,
                debounceMs: 250
            });

            vm.userConfig = TypeAheadService.createConfig({
                dataType: 'users',
                placeholder: 'Search for users...',
                minLength: 2,
                debounceMs: 300
            });
        }

        function onCountrySelect(item) {
            console.log('Country selected:', item);
            addToSelectedItems('Country', item);
        }

        function onCitySelect(item) {
            console.log('City selected:', item);
            addToSelectedItems('City', item);
        }

        function onProductSelect(item) {
            console.log('Product selected:', item);
            addToSelectedItems('Product', item);
        }

        function onUserSelect(item) {
            console.log('User selected:', item);
            addToSelectedItems('User', item);
        }

        function addToSelectedItems(type, item) {
            var selection = {
                type: type,
                item: item,
                timestamp: new Date(),
                displayText: item.getDisplayText(),
                subText: item.getSubText()
            };

            vm.selectedItems.unshift(selection);
            
            // Keep only last 10 selections
            if (vm.selectedItems.length > 10) {
                vm.selectedItems = vm.selectedItems.slice(0, 10);
            }
        }

        function clearSelections() {
            vm.selectedItems = [];
            vm.selectedCountry = '';
            vm.selectedCity = '';
            vm.selectedProduct = '';
            vm.selectedUser = '';
        }

        function getDataTypeLabel(dataType) {
            var type = vm.dataTypes.find(function(dt) {
                return dt.value === dataType;
            });
            return type ? type.label : dataType;
        }
    }

})();
