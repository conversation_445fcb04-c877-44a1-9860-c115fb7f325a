body {
    padding-top: 50px;
    padding-bottom: 20px;
}

/* Set padding to keep content from hitting the edges */
.body-content {
    padding-left: 15px;
    padding-right: 15px;
}

/* Override the default bootstrap behavior where horizontal description lists 
   will truncate terms that are too long to fit in the left column 
*/
.dl-horizontal dt {
    white-space: normal;
}

/* Set width on the form input elements since they're 100% wide by default */
input,
select,
textarea {
    max-width: 280px;
}

/* Angular specific styles */
.ng-invalid.ng-dirty {
    border-color: #d9534f;
}

.ng-valid.ng-dirty {
    border-color: #5cb85c;
}

/* Loading animation */
.glyphicon-refresh {
    animation: spin 1s infinite linear;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Custom styles for the demo */
.panel {
    margin-top: 20px;
}

.form-horizontal .form-group {
    margin-bottom: 15px;
}

.alert {
    margin-top: 15px;
}
