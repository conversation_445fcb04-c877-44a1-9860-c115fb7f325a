body {
    padding-top: 50px;
    padding-bottom: 20px;
}

/* Set padding to keep content from hitting the edges */
.body-content {
    padding-left: 15px;
    padding-right: 15px;
}

/* Override the default bootstrap behavior where horizontal description lists 
   will truncate terms that are too long to fit in the left column 
*/
.dl-horizontal dt {
    white-space: normal;
}

/* Set width on the form input elements since they're 100% wide by default */
input,
select,
textarea {
    max-width: 280px;
}

/* Angular specific styles */
.ng-invalid.ng-dirty {
    border-color: #d9534f;
}

.ng-valid.ng-dirty {
    border-color: #5cb85c;
}

/* Loading animation */
.glyphicon-refresh {
    animation: spin 1s infinite linear;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Custom styles for the demo */
.panel {
    margin-top: 20px;
}

.form-horizontal .form-group {
    margin-bottom: 15px;
}

.alert {
    margin-top: 15px;
}

/* Type-ahead styles */
.type-ahead-container {
    position: relative;
    display: block;
}

.type-ahead-input {
    width: 100%;
}

.type-ahead-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    z-index: 1000;
    background-color: #fff;
    border: 1px solid #ccc;
    border-top: none;
    border-radius: 0 0 4px 4px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    max-height: 300px;
    overflow-y: auto;
}

.type-ahead-error {
    padding: 10px 15px;
    color: #a94442;
    background-color: #f2dede;
    border-bottom: 1px solid #ebccd1;
}

.type-ahead-results {
    list-style: none;
    margin: 0;
    padding: 0;
}

.type-ahead-result-item {
    padding: 8px 15px;
    cursor: pointer;
    border-bottom: 1px solid #eee;
    transition: background-color 0.2s ease;
}

.type-ahead-result-item:hover,
.type-ahead-result-item.highlighted {
    background-color: #f5f5f5;
}

.type-ahead-result-item:last-child {
    border-bottom: none;
}

.type-ahead-result-item .result-main {
    font-weight: 500;
    color: #333;
}

.type-ahead-result-item .result-sub {
    font-size: 12px;
    color: #666;
    margin-top: 2px;
}

.type-ahead-footer {
    padding: 5px 15px;
    background-color: #f9f9f9;
    border-top: 1px solid #eee;
    text-align: right;
}

.input-group-addon .glyphicon-refresh {
    animation: spin 1s infinite linear;
}
