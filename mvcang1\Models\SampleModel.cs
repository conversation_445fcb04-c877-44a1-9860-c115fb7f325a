using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace MvcAng1.Models
{
    public class SampleModel
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(100)]
        public string Name { get; set; }
        
        [StringLength(500)]
        public string Description { get; set; }
        
        public DateTime CreatedDate { get; set; }
        
        public bool IsActive { get; set; }
    }

    public class ApiResponse<T>
    {
        public bool Success { get; set; }
        public string Message { get; set; }
        public T Data { get; set; }
        public DateTime Timestamp { get; set; }

        public ApiResponse()
        {
            Timestamp = DateTime.Now;
        }
    }

    public class SampleDataRequest
    {
        [Required]
        public string Name { get; set; }
        
        public string Description { get; set; }
    }
}
