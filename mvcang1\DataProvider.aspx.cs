using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using Newtonsoft.Json;

namespace MvcAng1
{
    public partial class DataProvider : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            // Set response content type to JSON
            Response.ContentType = "application/json";
            Response.Clear();

            try
            {
                string searchTerm = Request.QueryString["search"] ?? "";
                string dataType = Request.QueryString["type"] ?? "countries";
                
                var data = GetMockData(dataType, searchTerm);
                
                string jsonResponse = JsonConvert.SerializeObject(new
                {
                    success = true,
                    data = data,
                    searchTerm = searchTerm,
                    timestamp = DateTime.Now,
                    source = "ASPX DataProvider"
                });

                Response.Write(jsonResponse);
            }
            catch (Exception ex)
            {
                string errorResponse = JsonConvert.SerializeObject(new
                {
                    success = false,
                    error = ex.Message,
                    timestamp = DateTime.Now,
                    source = "ASPX DataProvider"
                });

                Response.Write(errorResponse);
            }
            finally
            {
                Response.End();
            }
        }

        private List<object> GetMockData(string dataType, string searchTerm)
        {
            var allData = new List<object>();

            switch (dataType.ToLower())
            {
                case "countries":
                    allData = GetCountriesData();
                    break;
                case "cities":
                    allData = GetCitiesData();
                    break;
                case "products":
                    allData = GetProductsData();
                    break;
                case "users":
                    allData = GetUsersData();
                    break;
                default:
                    allData = GetCountriesData();
                    break;
            }

            // Filter data based on search term
            if (!string.IsNullOrEmpty(searchTerm))
            {
                searchTerm = searchTerm.ToLower();
                allData = allData.Where(item =>
                {
                    var itemDict = item as dynamic;
                    string name = itemDict.name?.ToString()?.ToLower() ?? "";
                    string code = itemDict.code?.ToString()?.ToLower() ?? "";
                    return name.Contains(searchTerm) || code.Contains(searchTerm);
                }).ToList();
            }

            return allData.Take(10).ToList(); // Limit to 10 results for performance
        }

        private List<object> GetCountriesData()
        {
            return new List<object>
            {
                new { id = 1, name = "United States", code = "US", region = "North America" },
                new { id = 2, name = "Canada", code = "CA", region = "North America" },
                new { id = 3, name = "United Kingdom", code = "GB", region = "Europe" },
                new { id = 4, name = "Germany", code = "DE", region = "Europe" },
                new { id = 5, name = "France", code = "FR", region = "Europe" },
                new { id = 6, name = "Japan", code = "JP", region = "Asia" },
                new { id = 7, name = "Australia", code = "AU", region = "Oceania" },
                new { id = 8, name = "Brazil", code = "BR", region = "South America" },
                new { id = 9, name = "India", code = "IN", region = "Asia" },
                new { id = 10, name = "China", code = "CN", region = "Asia" },
                new { id = 11, name = "Mexico", code = "MX", region = "North America" },
                new { id = 12, name = "Italy", code = "IT", region = "Europe" },
                new { id = 13, name = "Spain", code = "ES", region = "Europe" },
                new { id = 14, name = "Netherlands", code = "NL", region = "Europe" },
                new { id = 15, name = "Sweden", code = "SE", region = "Europe" }
            };
        }

        private List<object> GetCitiesData()
        {
            return new List<object>
            {
                new { id = 1, name = "New York", code = "NYC", country = "United States", population = 8419000 },
                new { id = 2, name = "London", code = "LON", country = "United Kingdom", population = 8982000 },
                new { id = 3, name = "Tokyo", code = "TYO", country = "Japan", population = 13929000 },
                new { id = 4, name = "Paris", code = "PAR", country = "France", population = 2161000 },
                new { id = 5, name = "Berlin", code = "BER", country = "Germany", population = 3669000 },
                new { id = 6, name = "Sydney", code = "SYD", country = "Australia", population = 5312000 },
                new { id = 7, name = "Toronto", code = "TOR", country = "Canada", population = 2930000 },
                new { id = 8, name = "Mumbai", code = "BOM", country = "India", population = 20411000 },
                new { id = 9, name = "São Paulo", code = "SAO", country = "Brazil", population = 12325000 },
                new { id = 10, name = "Shanghai", code = "SHA", country = "China", population = 24281000 }
            };
        }

        private List<object> GetProductsData()
        {
            return new List<object>
            {
                new { id = 1, name = "Laptop Computer", code = "LAP001", category = "Electronics", price = 999.99 },
                new { id = 2, name = "Smartphone", code = "PHN001", category = "Electronics", price = 699.99 },
                new { id = 3, name = "Coffee Maker", code = "COF001", category = "Appliances", price = 89.99 },
                new { id = 4, name = "Office Chair", code = "CHR001", category = "Furniture", price = 199.99 },
                new { id = 5, name = "Wireless Mouse", code = "MOU001", category = "Electronics", price = 29.99 },
                new { id = 6, name = "Desk Lamp", code = "LAM001", category = "Lighting", price = 49.99 },
                new { id = 7, name = "Bluetooth Speaker", code = "SPK001", category = "Electronics", price = 79.99 },
                new { id = 8, name = "Water Bottle", code = "BOT001", category = "Accessories", price = 19.99 },
                new { id = 9, name = "Notebook Set", code = "NOT001", category = "Stationery", price = 15.99 },
                new { id = 10, name = "USB Cable", code = "USB001", category = "Electronics", price = 12.99 }
            };
        }

        private List<object> GetUsersData()
        {
            return new List<object>
            {
                new { id = 1, name = "John Smith", code = "JSMITH", email = "<EMAIL>", department = "IT" },
                new { id = 2, name = "Jane Doe", code = "JDOE", email = "<EMAIL>", department = "HR" },
                new { id = 3, name = "Mike Johnson", code = "MJOHNSON", email = "<EMAIL>", department = "Sales" },
                new { id = 4, name = "Sarah Wilson", code = "SWILSON", email = "<EMAIL>", department = "Marketing" },
                new { id = 5, name = "David Brown", code = "DBROWN", email = "<EMAIL>", department = "Finance" },
                new { id = 6, name = "Lisa Garcia", code = "LGARCIA", email = "<EMAIL>", department = "Operations" },
                new { id = 7, name = "Tom Anderson", code = "TANDERSON", email = "<EMAIL>", department = "IT" },
                new { id = 8, name = "Emily Davis", code = "EDAVIS", email = "<EMAIL>", department = "HR" },
                new { id = 9, name = "Chris Miller", code = "CMILLER", email = "<EMAIL>", department = "Sales" },
                new { id = 10, name = "Amanda Taylor", code = "ATAYLOR", email = "<EMAIL>", department = "Marketing" }
            };
        }
    }
}
