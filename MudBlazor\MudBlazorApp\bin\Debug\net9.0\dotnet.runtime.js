//! Licensed to the .NET Foundation under one or more agreements.
//! The .NET Foundation licenses this file to you under the MIT license.
var e="9.0.3",t="Release",n=!1;const r=[[!0,"mono_wasm_register_root","number",["number","number","string"]],[!0,"mono_wasm_deregister_root",null,["number"]],[!0,"mono_wasm_string_get_data_ref",null,["number","number","number","number"]],[!0,"mono_wasm_set_is_debugger_attached","void",["bool"]],[!0,"mono_wasm_send_dbg_command","bool",["number","number","number","number","number"]],[!0,"mono_wasm_send_dbg_command_with_parms","bool",["number","number","number","number","number","number","string"]],[!0,"mono_wasm_setenv",null,["string","string"]],[!0,"mono_wasm_parse_runtime_options",null,["number","number"]],[!0,"mono_wasm_strdup","number",["string"]],[!0,"mono_background_exec",null,[]],[!0,"mono_wasm_execute_timer",null,[]],[!0,"mono_wasm_load_icu_data","number",["number"]],[!1,"mono_wasm_add_assembly","number",["string","number","number"]],[!0,"mono_wasm_add_satellite_assembly","void",["string","string","number","number"]],[!1,"mono_wasm_load_runtime",null,["number"]],[!0,"mono_wasm_change_debugger_log_level","void",["number"]],[!0,"mono_wasm_assembly_load","number",["string"]],[!0,"mono_wasm_assembly_find_class","number",["number","string","string"]],[!0,"mono_wasm_assembly_find_method","number",["number","string","number"]],[!0,"mono_wasm_string_from_utf16_ref","void",["number","number","number"]],[!0,"mono_wasm_intern_string_ref","void",["number"]],[!1,"mono_wasm_exit","void",["number"]],[!0,"mono_wasm_getenv","number",["string"]],[!0,"mono_wasm_set_main_args","void",["number","number"]],[()=>!ot.emscriptenBuildOptions.enableAotProfiler,"mono_wasm_profiler_init_aot","void",["string"]],[()=>!ot.emscriptenBuildOptions.enableBrowserProfiler,"mono_wasm_profiler_init_browser","void",["string"]],[()=>!ot.emscriptenBuildOptions.enableLogProfiler,"mono_wasm_profiler_init_log","void",["string"]],[!0,"mono_wasm_profiler_init_browser","void",["number"]],[!1,"mono_wasm_exec_regression","number",["number","string"]],[!1,"mono_wasm_invoke_jsexport","void",["number","number"]],[!0,"mono_wasm_write_managed_pointer_unsafe","void",["number","number"]],[!0,"mono_wasm_copy_managed_pointer","void",["number","number"]],[!0,"mono_wasm_i52_to_f64","number",["number","number"]],[!0,"mono_wasm_u52_to_f64","number",["number","number"]],[!0,"mono_wasm_f64_to_i52","number",["number","number"]],[!0,"mono_wasm_f64_to_u52","number",["number","number"]],[!0,"mono_wasm_method_get_name","number",["number"]],[!0,"mono_wasm_method_get_full_name","number",["number"]],[!0,"mono_wasm_gc_lock","void",[]],[!0,"mono_wasm_gc_unlock","void",[]],[!0,"mono_wasm_get_i32_unaligned","number",["number"]],[!0,"mono_wasm_get_f32_unaligned","number",["number"]],[!0,"mono_wasm_get_f64_unaligned","number",["number"]],[!0,"mono_wasm_read_as_bool_or_null_unsafe","number",["number"]],[!0,"mono_jiterp_trace_bailout","void",["number"]],[!0,"mono_jiterp_get_trace_bailout_count","number",["number"]],[!0,"mono_jiterp_value_copy","void",["number","number","number"]],[!0,"mono_jiterp_get_member_offset","number",["number"]],[!0,"mono_jiterp_encode_leb52","number",["number","number","number"]],[!0,"mono_jiterp_encode_leb64_ref","number",["number","number","number"]],[!0,"mono_jiterp_encode_leb_signed_boundary","number",["number","number","number"]],[!0,"mono_jiterp_write_number_unaligned","void",["number","number","number"]],[!0,"mono_jiterp_type_is_byref","number",["number"]],[!0,"mono_jiterp_get_size_of_stackval","number",[]],[!0,"mono_jiterp_parse_option","number",["string"]],[!0,"mono_jiterp_get_options_as_json","number",[]],[!0,"mono_jiterp_get_option_as_int","number",["string"]],[!0,"mono_jiterp_get_options_version","number",[]],[!0,"mono_jiterp_adjust_abort_count","number",["number","number"]],[!0,"mono_jiterp_register_jit_call_thunk","void",["number","number"]],[!0,"mono_jiterp_type_get_raw_value_size","number",["number"]],[!0,"mono_jiterp_get_signature_has_this","number",["number"]],[!0,"mono_jiterp_get_signature_return_type","number",["number"]],[!0,"mono_jiterp_get_signature_param_count","number",["number"]],[!0,"mono_jiterp_get_signature_params","number",["number"]],[!0,"mono_jiterp_type_to_ldind","number",["number"]],[!0,"mono_jiterp_type_to_stind","number",["number"]],[!0,"mono_jiterp_imethod_to_ftnptr","number",["number"]],[!0,"mono_jiterp_debug_count","number",[]],[!0,"mono_jiterp_get_trace_hit_count","number",["number"]],[!0,"mono_jiterp_get_polling_required_address","number",[]],[!0,"mono_jiterp_get_rejected_trace_count","number",[]],[!0,"mono_jiterp_boost_back_branch_target","void",["number"]],[!0,"mono_jiterp_is_imethod_var_address_taken","number",["number","number"]],[!0,"mono_jiterp_get_opcode_value_table_entry","number",["number"]],[!0,"mono_jiterp_get_simd_intrinsic","number",["number","number"]],[!0,"mono_jiterp_get_simd_opcode","number",["number","number"]],[!0,"mono_jiterp_get_arg_offset","number",["number","number","number"]],[!0,"mono_jiterp_get_opcode_info","number",["number","number"]],[!0,"mono_wasm_is_zero_page_reserved","number",[]],[!0,"mono_jiterp_is_special_interface","number",["number"]],[!0,"mono_jiterp_initialize_table","void",["number","number","number"]],[!0,"mono_jiterp_allocate_table_entry","number",["number"]],[!0,"mono_jiterp_get_interp_entry_func","number",["number"]],[!0,"mono_jiterp_get_counter","number",["number"]],[!0,"mono_jiterp_modify_counter","number",["number","number"]],[!0,"mono_jiterp_tlqueue_next","number",["number"]],[!0,"mono_jiterp_tlqueue_add","number",["number","number"]],[!0,"mono_jiterp_tlqueue_clear","void",["number"]],[!0,"mono_jiterp_begin_catch","void",["number"]],[!0,"mono_jiterp_end_catch","void",[]],[!0,"mono_interp_pgo_load_table","number",["number","number"]],[!0,"mono_interp_pgo_save_table","number",["number","number"]]],o={},s=o,a=["void","number",null];function i(e,t,n,r){let o=void 0===r&&a.indexOf(t)>=0&&(!n||n.every((e=>a.indexOf(e)>=0)))&&Xe.wasmExports?Xe.wasmExports[e]:void 0;if(o&&n&&o.length!==n.length&&(Pe(`argument count mismatch for cwrap ${e}`),o=void 0),"function"!=typeof o&&(o=Xe.cwrap(e,t,n,r)),"function"!=typeof o)throw new Error(`cwrap ${e} not found or not a function`);return o}const c=0,l=0,p=0,u=BigInt("9223372036854775807"),d=BigInt("-9223372036854775808");function f(e,t,n){if(!Number.isSafeInteger(e))throw new Error(`Assert failed: Value is not an integer: ${e} (${typeof e})`);if(!(e>=t&&e<=n))throw new Error(`Assert failed: Overflow: value ${e} is out of ${t} ${n} range`)}function _(e,t){Y().fill(0,e,e+t)}function m(e,t){const n=!!t;"number"==typeof t&&f(t,0,1),Xe.HEAP32[e>>>2]=n?1:0}function h(e,t){const n=!!t;"number"==typeof t&&f(t,0,1),Xe.HEAPU8[e]=n?1:0}function g(e,t){f(t,0,255),Xe.HEAPU8[e]=t}function b(e,t){f(t,0,65535),Xe.HEAPU16[e>>>1]=t}function y(e,t,n){f(n,0,65535),e[t>>>1]=n}function w(e,t){f(t,0,4294967295),Xe.HEAPU32[e>>>2]=t}function k(e,t){f(t,-128,127),Xe.HEAP8[e]=t}function S(e,t){f(t,-32768,32767),Xe.HEAP16[e>>>1]=t}function v(e,t){f(t,-2147483648,2147483647),Xe.HEAP32[e>>>2]=t}function U(e){if(0!==e)switch(e){case 1:throw new Error("value was not an integer");case 2:throw new Error("value out of range");default:throw new Error("unknown internal error")}}function E(e,t){if(!Number.isSafeInteger(t))throw new Error(`Assert failed: Value is not a safe integer: ${t} (${typeof t})`);U(o.mono_wasm_f64_to_i52(e,t))}function T(e,t){if(!Number.isSafeInteger(t))throw new Error(`Assert failed: Value is not a safe integer: ${t} (${typeof t})`);if(!(t>=0))throw new Error("Assert failed: Can't convert negative Number into UInt64");U(o.mono_wasm_f64_to_u52(e,t))}function x(e,t){if("bigint"!=typeof t)throw new Error(`Assert failed: Value is not an bigint: ${t} (${typeof t})`);if(!(t>=d&&t<=u))throw new Error(`Assert failed: Overflow: value ${t} is out of ${d} ${u} range`);Xe.HEAP64[e>>>3]=t}function I(e,t){if("number"!=typeof t)throw new Error(`Assert failed: Value is not a Number: ${t} (${typeof t})`);Xe.HEAPF32[e>>>2]=t}function A(e,t){if("number"!=typeof t)throw new Error(`Assert failed: Value is not a Number: ${t} (${typeof t})`);Xe.HEAPF64[e>>>3]=t}let j=!0;function $(e){const t=Xe.HEAPU32[e>>>2];return t>1&&j&&(j=!1,Me(`getB32: value at ${e} is not a boolean, but a number: ${t}`)),!!t}function L(e){return!!Xe.HEAPU8[e]}function R(e){return Xe.HEAPU8[e]}function B(e){return Xe.HEAPU16[e>>>1]}function N(e){return Xe.HEAPU32[e>>>2]}function C(e,t){return e[t>>>2]}function O(e){return o.mono_wasm_get_i32_unaligned(e)}function D(e){return o.mono_wasm_get_i32_unaligned(e)>>>0}function F(e){return Xe.HEAP8[e]}function M(e){return Xe.HEAP16[e>>>1]}function P(e){return Xe.HEAP32[e>>>2]}function V(e){const t=o.mono_wasm_i52_to_f64(e,ot._i52_error_scratch_buffer);return U(P(ot._i52_error_scratch_buffer)),t}function z(e){const t=o.mono_wasm_u52_to_f64(e,ot._i52_error_scratch_buffer);return U(P(ot._i52_error_scratch_buffer)),t}function H(e){return Xe.HEAP64[e>>>3]}function W(e){return Xe.HEAPF32[e>>>2]}function q(e){return Xe.HEAPF64[e>>>3]}function G(){return Xe.HEAP8}function J(){return Xe.HEAP16}function X(){return Xe.HEAP32}function Q(){return Xe.HEAP64}function Y(){return Xe.HEAPU8}function Z(){return Xe.HEAPU16}function K(){return Xe.HEAPU32}function ee(){return Xe.HEAPF32}function te(){return Xe.HEAPF64}let ne=!1;function re(){if(ne)throw new Error("GC is already locked");ne=!0}function oe(){if(!ne)throw new Error("GC is not locked");ne=!1}const se=8192;let ae=null,ie=null,ce=0;const le=[],pe=[];function ue(e,t){if(e<=0)throw new Error("capacity >= 1");const n=4*(e|=0),r=Xe._malloc(n);if(r%4!=0)throw new Error("Malloc returned an unaligned offset");return _(r,n),new WasmRootBufferImpl(r,e,!0,t)}class WasmRootBufferImpl{constructor(e,t,n,r){const s=4*t;this.__offset=e,this.__offset32=e>>>2,this.__count=t,this.length=t,this.__handle=o.mono_wasm_register_root(e,s,r||"noname"),this.__ownsAllocation=n}_throw_index_out_of_range(){throw new Error("index out of range")}_check_in_range(e){(e>=this.__count||e<0)&&this._throw_index_out_of_range()}get_address(e){return this._check_in_range(e),this.__offset+4*e}get_address_32(e){return this._check_in_range(e),this.__offset32+e}get(e){this._check_in_range(e);const t=this.get_address_32(e);return K()[t]}set(e,t){const n=this.get_address(e);return o.mono_wasm_write_managed_pointer_unsafe(n,t),t}copy_value_from_address(e,t){const n=this.get_address(e);o.mono_wasm_copy_managed_pointer(n,t)}_unsafe_get(e){return K()[this.__offset32+e]}_unsafe_set(e,t){const n=this.__offset+e;o.mono_wasm_write_managed_pointer_unsafe(n,t)}clear(){this.__offset&&_(this.__offset,4*this.__count)}release(){this.__offset&&this.__ownsAllocation&&(o.mono_wasm_deregister_root(this.__offset),_(this.__offset,4*this.__count),Xe._free(this.__offset)),this.__handle=this.__offset=this.__count=this.__offset32=0}toString(){return`[root buffer @${this.get_address(0)}, size ${this.__count} ]`}}class de{constructor(e,t){this.__buffer=e,this.__index=t}get_address(){return this.__buffer.get_address(this.__index)}get_address_32(){return this.__buffer.get_address_32(this.__index)}get address(){return this.__buffer.get_address(this.__index)}get(){return this.__buffer._unsafe_get(this.__index)}set(e){const t=this.__buffer.get_address(this.__index);return o.mono_wasm_write_managed_pointer_unsafe(t,e),e}copy_from(e){const t=e.address,n=this.address;o.mono_wasm_copy_managed_pointer(n,t)}copy_to(e){const t=this.address,n=e.address;o.mono_wasm_copy_managed_pointer(n,t)}copy_from_address(e){const t=this.address;o.mono_wasm_copy_managed_pointer(t,e)}copy_to_address(e){const t=this.address;o.mono_wasm_copy_managed_pointer(e,t)}get value(){return this.get()}set value(e){this.set(e)}valueOf(){throw new Error("Implicit conversion of roots to pointers is no longer supported. Use .value or .address as appropriate")}clear(){const e=this.__buffer.get_address_32(this.__index);K()[e]=0}release(){if(!this.__buffer)throw new Error("No buffer");var e;le.length>128?(void 0!==(e=this.__index)&&(ae.set(e,0),ie[ce]=e,ce++),this.__buffer=null,this.__index=0):(this.set(0),le.push(this))}toString(){return`[root @${this.address}]`}}class fe{constructor(e){this.__external_address=0,this.__external_address_32=0,this._set_address(e)}_set_address(e){this.__external_address=e,this.__external_address_32=e>>>2}get address(){return this.__external_address}get_address(){return this.__external_address}get_address_32(){return this.__external_address_32}get(){return K()[this.__external_address_32]}set(e){return o.mono_wasm_write_managed_pointer_unsafe(this.__external_address,e),e}copy_from(e){const t=e.address,n=this.__external_address;o.mono_wasm_copy_managed_pointer(n,t)}copy_to(e){const t=this.__external_address,n=e.address;o.mono_wasm_copy_managed_pointer(n,t)}copy_from_address(e){const t=this.__external_address;o.mono_wasm_copy_managed_pointer(t,e)}copy_to_address(e){const t=this.__external_address;o.mono_wasm_copy_managed_pointer(e,t)}get value(){return this.get()}set value(e){this.set(e)}valueOf(){throw new Error("Implicit conversion of roots to pointers is no longer supported. Use .value or .address as appropriate")}clear(){K()[this.__external_address>>>2]=0}release(){pe.length<128&&pe.push(this)}toString(){return`[external root @${this.address}]`}}const _e=new Map,me="";let he;const ge=new Map;let be,ye,we,ke,Se,ve=0,Ue=null,Ee=0;function Te(e){if(void 0===ke){const t=Xe.lengthBytesUTF8(e),n=new Uint8Array(t);return Xe.stringToUTF8Array(e,n,0,t),n}return ke.encode(e)}function xe(e){const t=Y();return function(e,t,n){const r=t+n;let o=t;for(;e[o]&&!(o>=r);)++o;if(o-t<=16)return Xe.UTF8ArrayToString(e,t,n);if(void 0===we)return Xe.UTF8ArrayToString(e,t,n);const s=Ne(e,t,o);return we.decode(s)}(t,e,t.length-e)}function Ie(e,t){if(be){const n=Ne(Y(),e,t);return be.decode(n)}return Ae(e,t)}function Ae(e,t){let n="";const r=Z();for(let o=e;o<t;o+=2){const e=r[o>>>1];n+=String.fromCharCode(e)}return n}function je(e,t,n){const r=Z(),o=n.length;for(let s=0;s<o&&(y(r,e,n.charCodeAt(s)),!((e+=2)>=t));s++);}function $e(e){const t=2*(e.length+1),n=Xe._malloc(t);return _(n,2*e.length),je(n,n+t,e),n}function Le(e){if(e.value===l)return null;const t=he+0,n=he+4,r=he+8;let s;o.mono_wasm_string_get_data_ref(e.address,t,n,r);const a=K(),i=C(a,n),c=C(a,t),p=C(a,r);if(p&&(s=ge.get(e.value)),void 0===s&&(i&&c?(s=Ie(c,c+i),p&&ge.set(e.value,s)):s=me),void 0===s)throw new Error(`internal error when decoding string at location ${e.value}`);return s}function Re(e,t){let n;if("symbol"==typeof e?(n=e.description,"string"!=typeof n&&(n=Symbol.keyFor(e)),"string"!=typeof n&&(n="<unknown Symbol>")):"string"==typeof e&&(n=e),"string"!=typeof n)throw new Error(`Argument to stringToInternedMonoStringRoot must be a string but was ${e}`);if(0===n.length&&ve)return void t.set(ve);const r=_e.get(n);r?t.set(r):(Be(n,t),function(e,t,n){if(!t.value)throw new Error("null pointer passed to _store_string_in_intern_table");Ee>=8192&&(Ue=null),Ue||(Ue=ue(8192,"interned strings"),Ee=0);const r=Ue,s=Ee++;if(o.mono_wasm_intern_string_ref(t.address),!t.value)throw new Error("mono_wasm_intern_string_ref produced a null pointer");_e.set(e,t.value),ge.set(t.value,e),0!==e.length||ve||(ve=t.value),r.copy_value_from_address(s,t.address)}(n,t))}function Be(e,t){const n=2*(e.length+1),r=Xe._malloc(n);je(r,r+n,e),o.mono_wasm_string_from_utf16_ref(r,e.length,t.address),Xe._free(r)}function Ne(e,t,n){return e.buffer,e.subarray(t,n)}function Ce(e){if(e===l)return null;Se.value=e;const t=Le(Se);return Se.value=l,t}let Oe="MONO_WASM: ";function De(e){if(ot.diagnosticTracing){const t="function"==typeof e?e():e;console.debug(Oe+t)}}function Fe(e,...t){console.info(Oe+e,...t)}function Me(e,...t){console.warn(Oe+e,...t)}function Pe(e,...t){if(t&&t.length>0&&t[0]&&"object"==typeof t[0]){if(t[0].silent)return;if(t[0].toString)return void console.error(Oe+e,t[0].toString())}console.error(Oe+e,...t)}const Ve=new Map;let ze;const He=[];function We(e){try{if(Ge(),0==Ve.size)return e;const t=e;for(let n=0;n<He.length;n++){const r=e.replace(new RegExp(He[n],"g"),((e,...t)=>{const n=t.find((e=>"object"==typeof e&&void 0!==e.replaceSection));if(void 0===n)return e;const r=n.funcNum,o=n.replaceSection,s=Ve.get(Number(r));return void 0===s?e:e.replace(o,`${s} (${o})`)}));if(r!==t)return r}return t}catch(t){return console.debug(`failed to symbolicate: ${t}`),e}}function qe(e){let t;return t="string"==typeof e?e:null==e||void 0===e.stack?(new Error).stack+"":e.stack+"",We(t)}function Ge(){if(!ze)return;He.push(/at (?<replaceSection>[^:()]+:wasm-function\[(?<funcNum>\d+)\]:0x[a-fA-F\d]+)((?![^)a-fA-F\d])|$)/),He.push(/(?:WASM \[[\da-zA-Z]+\], (?<replaceSection>function #(?<funcNum>[\d]+) \(''\)))/),He.push(/(?<replaceSection>[a-z]+:\/\/[^ )]*:wasm-function\[(?<funcNum>\d+)\]:0x[a-fA-F\d]+)/),He.push(/(?<replaceSection><[^ >]+>[.:]wasm-function\[(?<funcNum>[0-9]+)\])/);const e=ze;ze=void 0;try{e.split(/[\r\n]/).forEach((e=>{const t=e.split(/:/);t.length<2||(t[1]=t.splice(1).join(":"),Ve.set(Number(t[0]),t[1]))})),st.diagnosticTracing&&De(`Loaded ${Ve.size} symbols`)}catch(e){Me(`Failed to load symbol map: ${e}`)}}function Je(){return Ge(),[...Ve.values()]}let Xe,Qe;const Ye="object"==typeof process&&"object"==typeof process.versions&&"string"==typeof process.versions.node,Ze="function"==typeof importScripts,Ke=Ze&&"undefined"!=typeof dotnetSidecar,et=Ze&&!Ke,tt="object"==typeof window||Ze&&!Ye,nt=!tt&&!Ye;let rt=null,ot=null,st=null,at=null,it=!1;function ct(e,t){ot.emscriptenBuildOptions=t,e.isPThread,ot.quit=e.quit_,ot.ExitStatus=e.ExitStatus,ot.getMemory=e.getMemory,ot.getWasmIndirectFunctionTable=e.getWasmIndirectFunctionTable,ot.updateMemoryViews=e.updateMemoryViews}function lt(e){if(it)throw new Error("Runtime module already loaded");it=!0,Xe=e.module,Qe=e.internal,ot=e.runtimeHelpers,st=e.loaderHelpers,at=e.globalizationHelpers,rt=e.api;const t={gitHash:"831d23e56149cd59c40fc00c7feb7c5334bd19c4",coreAssetsInMemory:pt(),allAssetsInMemory:pt(),dotnetReady:pt(),afterInstantiateWasm:pt(),beforePreInit:pt(),afterPreInit:pt(),afterPreRun:pt(),beforeOnRuntimeInitialized:pt(),afterMonoStarted:pt(),afterDeputyReady:pt(),afterIOStarted:pt(),afterOnRuntimeInitialized:pt(),afterPostRun:pt(),nativeAbort:e=>{throw e||new Error("abort")},nativeExit:e=>{throw new Error("exit:"+e)}};Object.assign(ot,t),Object.assign(e.module.config,{}),Object.assign(e.api,{Module:e.module,...e.module}),Object.assign(e.api,{INTERNAL:e.internal})}function pt(e,t){return st.createPromiseController(e,t)}function ut(e,t){if(e)return;const n="Assert failed: "+("function"==typeof t?t():t),r=new Error(n);Pe(n,r),ot.nativeAbort(r)}function dt(e,t,n){const r=function(e,t,n){let r,o=0;r=e.length-o;const s={read:function(){if(o>=r)return null;const t=e[o];return o+=1,t}};return Object.defineProperty(s,"eof",{get:function(){return o>=r},configurable:!0,enumerable:!0}),s}(e);let o="",s=0,a=0,i=0,c=0,l=0,p=0;for(;s=r.read(),a=r.read(),i=r.read(),null!==s;)null===a&&(a=0,l+=1),null===i&&(i=0,l+=1),p=s<<16|a<<8|i,c=(16777215&p)>>18,o+=ft[c],c=(262143&p)>>12,o+=ft[c],l<2&&(c=(4095&p)>>6,o+=ft[c]),2===l?o+="==":1===l?o+="=":(c=63&p,o+=ft[c]);return o}const ft=["A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z","a","b","c","d","e","f","g","h","i","j","k","l","m","n","o","p","q","r","s","t","u","v","w","x","y","z","0","1","2","3","4","5","6","7","8","9","+","/"],_t=new Map;_t.remove=function(e){const t=this.get(e);return this.delete(e),t};let mt,ht,gt,bt={},yt=0,wt=-1;function mono_wasm_fire_debugger_agent_message_with_data_to_pause(e){console.assert(!0,`mono_wasm_fire_debugger_agent_message_with_data ${e}`);debugger}function kt(e){e.length>wt&&(mt&&Xe._free(mt),wt=Math.max(e.length,wt,256),mt=Xe._malloc(wt));const t=atob(e),n=Y();for(let e=0;e<t.length;e++)n[mt+e]=t.charCodeAt(e)}function St(e,t,n,r,s,a,i){kt(r),o.mono_wasm_send_dbg_command_with_parms(e,t,n,mt,s,a,i.toString());const{res_ok:c,res:l}=_t.remove(e);if(!c)throw new Error("Failed on mono_wasm_send_dbg_command_with_parms");return l}function vt(e,t,n,r){kt(r),o.mono_wasm_send_dbg_command(e,t,n,mt,r.length);const{res_ok:s,res:a}=_t.remove(e);if(!s)throw new Error("Failed on mono_wasm_send_dbg_command");return a}function Ut(){const{res_ok:e,res:t}=_t.remove(0);if(!e)throw new Error("Failed on mono_wasm_get_dbg_command_info");return t}function Et(){}function Tt(){o.mono_wasm_set_is_debugger_attached(!1)}function xt(e){o.mono_wasm_change_debugger_log_level(e)}function It(e,t={}){if("object"!=typeof e)throw new Error(`event must be an object, but got ${JSON.stringify(e)}`);if(void 0===e.eventName)throw new Error(`event.eventName is a required parameter, in event: ${JSON.stringify(e)}`);if("object"!=typeof t)throw new Error(`args must be an object, but got ${JSON.stringify(t)}`);console.debug("mono_wasm_debug_event_raised:aef14bca-5519-4dfe-b35a-f867abc123ae",JSON.stringify(e),JSON.stringify(t))}function At(){-1==ot.waitForDebugger&&(ot.waitForDebugger=1),o.mono_wasm_set_is_debugger_attached(!0)}function jt(e){if(null!=e.arguments&&!Array.isArray(e.arguments))throw new Error(`"arguments" should be an array, but was ${e.arguments}`);const t=e.objectId,n=e.details;let r={};if(t.startsWith("dotnet:cfo_res:")){if(!(t in bt))throw new Error(`Unknown object id ${t}`);r=bt[t]}else r=function(e,t){if(e.startsWith("dotnet:array:")){let e;if(void 0===t.items)return e=t.map((e=>e.value)),e;if(void 0===t.dimensionsDetails||1===t.dimensionsDetails.length)return e=t.items.map((e=>e.value)),e}const n={};return Object.keys(t).forEach((e=>{const r=t[e];void 0!==r.get?Object.defineProperty(n,r.name,{get:()=>vt(r.get.id,r.get.commandSet,r.get.command,r.get.buffer),set:function(e){return St(r.set.id,r.set.commandSet,r.set.command,r.set.buffer,r.set.length,r.set.valtype,e),!0}}):void 0!==r.set?Object.defineProperty(n,r.name,{get:()=>r.value,set:function(e){return St(r.set.id,r.set.commandSet,r.set.command,r.set.buffer,r.set.length,r.set.valtype,e),!0}}):n[r.name]=r.value})),n}(t,n);const o=null!=e.arguments?e.arguments.map((e=>JSON.stringify(e.value))):[],s=`const fn = ${e.functionDeclaration}; return fn.apply(proxy, [${o}]);`,a=new Function("proxy",s)(r);if(void 0===a)return{type:"undefined"};if(Object(a)!==a)return"object"==typeof a&&null==a?{type:typeof a,subtype:`${a}`,value:null}:{type:typeof a,description:`${a}`,value:`${a}`};if(e.returnByValue&&null==a.subtype)return{type:"object",value:a};if(Object.getPrototypeOf(a)==Array.prototype){const e=Lt(a);return{type:"object",subtype:"array",className:"Array",description:`Array(${a.length})`,objectId:e}}return void 0!==a.value||void 0!==a.subtype?a:a==r?{type:"object",className:"Object",description:"Object",objectId:t}:{type:"object",className:"Object",description:"Object",objectId:Lt(a)}}function $t(e,t={}){return function(e,t){if(!(e in bt))throw new Error(`Could not find any object with id ${e}`);const n=bt[e],r=Object.getOwnPropertyDescriptors(n);t.accessorPropertiesOnly&&Object.keys(r).forEach((e=>{void 0===r[e].get&&Reflect.deleteProperty(r,e)}));const o=[];return Object.keys(r).forEach((e=>{let t;const n=r[e];t="object"==typeof n.value?Object.assign({name:e},n):void 0!==n.value?{name:e,value:Object.assign({type:typeof n.value,description:""+n.value},n)}:void 0!==n.get?{name:e,get:{className:"Function",description:`get ${e} () {}`,type:"function"}}:{name:e,value:{type:"symbol",value:"<Unknown>",description:"<Unknown>"}},o.push(t)})),{__value_as_json_string__:JSON.stringify(o)}}(`dotnet:cfo_res:${e}`,t)}function Lt(e){const t="dotnet:cfo_res:"+yt++;return bt[t]=e,t}function Rt(e){e in bt&&delete bt[e]}function Bt(){if(ot.enablePerfMeasure)return globalThis.performance.now()}function Nt(e,t,n){if(ot.enablePerfMeasure&&e){const r=tt?{start:e}:{startTime:e},o=n?`${t}${n} `:t;globalThis.performance.measure(o,r)}}const Ct=[],Ot=new Map;function Dt(e,t,n){if(0===t||1===t||2===t||26===t)return;let r,o,s,a;o=Yr(Rn(e)),s=Yr(Bn(e)),a=Yr(Nn(e));const i=Ln(e);r=Ft(i),19===t&&(t=i);const c=Ft(t),l=Rn(e),p=n*Un;return e=>c(e+p,l,r,o,s,a)}function Ft(e){if(0===e||1===e)return;const t=yn.get(e);return t&&"function"==typeof t||ut(!1,`ERR41: Unknown converter for type ${e}. ${Xr}`),t}function Mt(e){return 0==Dn(e)?null:function(e){return e||ut(!1,"Null arg"),L(e)}(e)}function Pt(e){return 0==Dn(e)?null:function(e){return e||ut(!1,"Null arg"),R(e)}(e)}function Vt(e){return 0==Dn(e)?null:function(e){return e||ut(!1,"Null arg"),B(e)}(e)}function zt(e){return 0==Dn(e)?null:function(e){return e||ut(!1,"Null arg"),M(e)}(e)}function Ht(e){return 0==Dn(e)?null:function(e){return e||ut(!1,"Null arg"),P(e)}(e)}function Wt(e){return 0==Dn(e)?null:function(e){return e||ut(!1,"Null arg"),q(e)}(e)}function qt(e){return 0==Dn(e)?null:function(e){return e||ut(!1,"Null arg"),H(e)}(e)}function Gt(e){return 0==Dn(e)?null:function(e){return e||ut(!1,"Null arg"),W(e)}(e)}function Jt(e){return 0==Dn(e)?null:function(e){return e||ut(!1,"Null arg"),q(e)}(e)}function Xt(e){return 0==Dn(e)?null:Pn(e)}function Qt(){return null}function Yt(e){return 0===Dn(e)?null:function(e){e||ut(!1,"Null arg");const t=q(e);return new Date(t)}(e)}function Zt(e,t,n,r,o,s){if(0===Dn(e))return null;const a=Jn(e);let i=Vr(a);return null==i&&(i=(e,t,i)=>function(e,t,n,r,o,s,a,i){st.assert_runtime_running();const c=Xe.stackSave();try{const c=xn(6),l=In(c,2);if(Mn(l,14),Xn(l,e),s&&s(In(c,3),t),a&&a(In(c,4),n),i&&i(In(c,5),r),gn(mn.CallDelegate,c),o)return o(In(c,1))}finally{Xe.stackRestore(c)}}(a,e,t,i,n,r,o,s),i.dispose=()=>{i.isDisposed||(i.isDisposed=!0,Fr(i,a))},i.isDisposed=!1,Dr(i,a)),i}class Kt{constructor(e,t){this.promise=e,this.resolve_or_reject=t}}function en(e,t,n){const r=Dn(e);30==r&&ut(!1,"Unexpected Task type: TaskPreCreated");const o=rn(e,r,n);if(!1!==o)return o;const s=qn(e),a=on(n);return function(e,t){dr(),vr[0-t]=e,Object.isExtensible(e)&&(e[Rr]=t)}(a,s),a.promise}function tn(e,t,n){const r=on(n);return Gn(e,Cr(r)),Mn(e,30),r.promise}function nn(e,t,n){const r=In(e,1),o=Dn(r);if(30===o)return n;Or(Cr(n));const s=rn(r,o,t);return!1===s&&ut(!1,`Expected synchronous result, got: ${o}`),s}function rn(e,t,n){if(0===t)return null;if(29===t)return Promise.reject(an(e));if(28===t){const t=Fn(e);if(1===t)return Promise.resolve();Mn(e,t),n||(n=yn.get(t)),n||ut(!1,`Unknown sub_converter for type ${t}. ${Xr}`);const r=n(e);return Promise.resolve(r)}return!1}function on(e){const{promise:t,promise_control:n}=st.createPromiseController();return new Kt(t,((t,r,o)=>{if(29===t){const e=an(o);n.reject(e)}else if(28===t){const t=Dn(o);if(1===t)n.resolve(void 0);else{e||(e=yn.get(t)),e||ut(!1,`Unknown sub_converter for type ${t}. ${Xr}`);const r=e(o);n.resolve(r)}}else ut(!1,`Unexpected type ${t}`);Or(r)}))}function sn(e){if(0==Dn(e))return null;{const t=Qn(e);try{return Le(t)}finally{t.release()}}}function an(e){const t=Dn(e);if(0==t)return null;if(27==t)return Nr(qn(e));const n=Jn(e);let r=Vr(n);if(null==r){const t=sn(e);r=new ManagedError(t),Dr(r,n)}return r}function cn(e){if(0==Dn(e))return null;const t=qn(e),n=Nr(t);return void 0===n&&ut(!1,`JS object JSHandle ${t} was not found`),n}function ln(e){const t=Dn(e);if(0==t)return null;if(13==t)return Nr(qn(e));if(21==t)return un(e,Fn(e));if(14==t){const t=Jn(e);if(t===p)return null;let n=Vr(t);return n||(n=new ManagedObject,Dr(n,t)),n}const n=yn.get(t);return n||ut(!1,`Unknown converter for type ${t}. ${Xr}`),n(e)}function pn(e,t){return t||ut(!1,"Expected valid element_type parameter"),un(e,t)}function un(e,t){if(0==Dn(e))return null;-1==Kn(t)&&ut(!1,`Element type ${t} not supported`);const n=Pn(e),r=Yn(e);let s=null;if(15==t){s=new Array(r);for(let e=0;e<r;e++){const t=In(n,e);s[e]=sn(t)}o.mono_wasm_deregister_root(n)}else if(14==t){s=new Array(r);for(let e=0;e<r;e++){const t=In(n,e);s[e]=ln(t)}o.mono_wasm_deregister_root(n)}else if(13==t){s=new Array(r);for(let e=0;e<r;e++){const t=In(n,e);s[e]=cn(t)}}else if(4==t)s=Y().subarray(n,n+r).slice();else if(7==t)s=X().subarray(n>>2,(n>>2)+r).slice();else{if(10!=t)throw new Error(`NotImplementedException ${t}. ${Xr}`);s=te().subarray(n>>3,(n>>3)+r).slice()}return Xe._free(n),s}function dn(e,t){t||ut(!1,"Expected valid element_type parameter");const n=Pn(e),r=Yn(e);let o=null;if(4==t)o=new Span(n,r,0);else if(7==t)o=new Span(n,r,1);else{if(10!=t)throw new Error(`NotImplementedException ${t}. ${Xr}`);o=new Span(n,r,2)}return o}function fn(e,t){t||ut(!1,"Expected valid element_type parameter");const n=Pn(e),r=Yn(e);let o=null;if(4==t)o=new ArraySegment(n,r,0);else if(7==t)o=new ArraySegment(n,r,1);else{if(10!=t)throw new Error(`NotImplementedException ${t}. ${Xr}`);o=new ArraySegment(n,r,2)}return Dr(o,Jn(e)),o}const _n={pthreadId:0,reuseCount:0,updateCount:0,threadPrefix:"          -    ",threadName:"emscripten-loaded"},mn={};function hn(e,t,n,r){if(dr(),o.mono_wasm_invoke_jsexport(t,n),An(n))throw an(In(n,0))}function gn(e,t){if(dr(),o.mono_wasm_invoke_jsexport(e,t),An(t))throw an(In(t,0))}function bn(e){const t=o.mono_wasm_assembly_find_method(ot.runtime_interop_exports_class,e,-1);if(!t)throw"Can't find method "+ot.runtime_interop_namespace+"."+ot.runtime_interop_exports_classname+"."+e;return t}const yn=new Map,wn=new Map,kn=Symbol.for("wasm bound_cs_function"),Sn=Symbol.for("wasm bound_js_function"),vn=Symbol.for("wasm imported_js_function"),Un=32,En=32,Tn=32;function xn(e){const t=Un*e,n=Xe.stackAlloc(t);return _(n,t),n}function In(e,t){return e||ut(!1,"Null args"),e+t*Un}function An(e){return e||ut(!1,"Null args"),0!==Dn(e)}function jn(e,t){return e||ut(!1,"Null signatures"),e+t*En+Tn}function $n(e){return e||ut(!1,"Null sig"),R(e+0)}function Ln(e){return e||ut(!1,"Null sig"),R(e+16)}function Rn(e){return e||ut(!1,"Null sig"),R(e+20)}function Bn(e){return e||ut(!1,"Null sig"),R(e+24)}function Nn(e){return e||ut(!1,"Null sig"),R(e+28)}function Cn(e){return e||ut(!1,"Null signatures"),P(e+4)}function On(e){return e||ut(!1,"Null signatures"),P(e+0)}function Dn(e){return e||ut(!1,"Null arg"),R(e+12)}function Fn(e){return e||ut(!1,"Null arg"),R(e+13)}function Mn(e,t){e||ut(!1,"Null arg"),g(e+12,t)}function Pn(e){return e||ut(!1,"Null arg"),P(e)}function Vn(e,t){if(e||ut(!1,"Null arg"),"boolean"!=typeof t)throw new Error(`Assert failed: Value is not a Boolean: ${t} (${typeof t})`);h(e,t)}function zn(e,t){e||ut(!1,"Null arg"),v(e,t)}function Hn(e,t){e||ut(!1,"Null arg"),A(e,t.getTime())}function Wn(e,t){e||ut(!1,"Null arg"),A(e,t)}function qn(e){return e||ut(!1,"Null arg"),P(e+4)}function Gn(e,t){e||ut(!1,"Null arg"),v(e+4,t)}function Jn(e){return e||ut(!1,"Null arg"),P(e+4)}function Xn(e,t){e||ut(!1,"Null arg"),v(e+4,t)}function Qn(e){return e||ut(!1,"Null arg"),function(e){let t;if(!e)throw new Error("address must be a location in the native heap");return pe.length>0?(t=pe.pop(),t._set_address(e)):t=new fe(e),t}(e)}function Yn(e){return e||ut(!1,"Null arg"),P(e+8)}function Zn(e,t){e||ut(!1,"Null arg"),v(e+8,t)}class ManagedObject{dispose(){Fr(this,p)}get isDisposed(){return this[Lr]===p}toString(){return`CsObject(gc_handle: ${this[Lr]})`}}class ManagedError extends Error{constructor(e){super(e),this.superStack=Object.getOwnPropertyDescriptor(this,"stack"),Object.defineProperty(this,"stack",{get:this.getManageStack})}getSuperStack(){if(this.superStack){if(void 0!==this.superStack.value)return this.superStack.value;if(void 0!==this.superStack.get)return this.superStack.get.call(this)}return super.stack}getManageStack(){if(this.managed_stack)return this.managed_stack;if(!st.is_runtime_running())return this.managed_stack="... omitted managed stack trace.\n"+this.getSuperStack(),this.managed_stack;{const e=this[Lr];if(e!==p){const t=function(e){st.assert_runtime_running();const t=Xe.stackSave();try{const t=xn(3),n=In(t,2);return Mn(n,16),Xn(n,e),gn(mn.GetManagedStackTrace,t),sn(In(t,1))}finally{Xe.stackRestore(t)}}(e);if(t)return this.managed_stack=t+"\n"+this.getSuperStack(),this.managed_stack}}return this.getSuperStack()}dispose(){Fr(this,p)}get isDisposed(){return this[Lr]===p}}function Kn(e){return 4==e?1:7==e?4:8==e||10==e?8:15==e||14==e||13==e?Un:-1}class er{constructor(e,t,n){this._pointer=e,this._length=t,this._viewType=n}_unsafe_create_view(){const e=0==this._viewType?new Uint8Array(Y().buffer,this._pointer,this._length):1==this._viewType?new Int32Array(X().buffer,this._pointer,this._length):2==this._viewType?new Float64Array(te().buffer,this._pointer,this._length):null;if(!e)throw new Error("NotImplementedException");return e}set(e,t){if(this.isDisposed)throw new Error("Assert failed: ObjectDisposedException");const n=this._unsafe_create_view();if(!e||!n||e.constructor!==n.constructor)throw new Error(`Assert failed: Expected ${n.constructor}`);n.set(e,t)}copyTo(e,t){if(this.isDisposed)throw new Error("Assert failed: ObjectDisposedException");const n=this._unsafe_create_view();if(!e||!n||e.constructor!==n.constructor)throw new Error(`Assert failed: Expected ${n.constructor}`);const r=n.subarray(t);e.set(r)}slice(e,t){if(this.isDisposed)throw new Error("Assert failed: ObjectDisposedException");return this._unsafe_create_view().slice(e,t)}get length(){if(this.isDisposed)throw new Error("Assert failed: ObjectDisposedException");return this._length}get byteLength(){if(this.isDisposed)throw new Error("Assert failed: ObjectDisposedException");return 0==this._viewType?this._length:1==this._viewType?this._length<<2:2==this._viewType?this._length<<3:0}}class Span extends er{constructor(e,t,n){super(e,t,n),this.is_disposed=!1}dispose(){this.is_disposed=!0}get isDisposed(){return this.is_disposed}}class ArraySegment extends er{constructor(e,t,n){super(e,t,n)}dispose(){Fr(this,p)}get isDisposed(){return this[Lr]===p}}const tr=[null];function nr(e){const t=e.args_count,r=e.arg_marshalers,o=e.res_converter,s=e.arg_cleanup,a=e.has_cleanup,i=e.fn,c=e.fqn;return e=null,function(l){const p=Bt();try{n&&e.isDisposed;const c=new Array(t);for(let e=0;e<t;e++){const t=(0,r[e])(l);c[e]=t}const p=i(...c);if(o&&o(l,p),a)for(let e=0;e<t;e++){const t=s[e];t&&t(c[e])}}catch(e){ho(l,e)}finally{Nt(p,"mono.callCsFunction:",c)}}}function rr(e,t){pr.set(e,t),st.diagnosticTracing&&De(`added module imports '${e}'`)}function or(e,t,n){if(!e)throw new Error("Assert failed: Null reference");e[t]=n}function sr(e,t){if(!e)throw new Error("Assert failed: Null reference");return e[t]}function ar(e,t){if(!e)throw new Error("Assert failed: Null reference");return t in e}function ir(e,t){if(!e)throw new Error("Assert failed: Null reference");return typeof e[t]}function cr(){return globalThis}const lr=new Map,pr=new Map;function ur(e,t){dr(),e&&"string"==typeof e||ut(!1,"module_name must be string"),t&&"string"==typeof t||ut(!1,"module_url must be string");let n=lr.get(e);const r=!n;return r&&(st.diagnosticTracing&&De(`importing ES6 module '${e}' from '${t}'`),n=import(/*! webpackIgnore: true */t),lr.set(e,n)),qr((async()=>{const o=await n;return r&&(pr.set(e,o),st.diagnosticTracing&&De(`imported ES6 module '${e}' from '${t}'`)),o}))}function dr(){st.assert_runtime_running(),ot.mono_wasm_bindings_is_ready||ut(!1,"The runtime must be initialized.")}function fr(e){e()}const _r="function"==typeof globalThis.WeakRef;function mr(e){return _r?new WeakRef(e):function(e){return{deref:()=>e,dispose:()=>{e=null}}}(e)}function hr(e,t,n,r,o,s,a){const i=`[${t}] ${n}.${r}:${o}`,c=Bt();st.diagnosticTracing&&De(`Binding [JSExport] ${n}.${r}:${o} from ${t} assembly`);const l=On(a);2!==l&&ut(!1,`Signature version ${l} mismatch.`);const p=Cn(a),u=new Array(p);for(let e=0;e<p;e++){const t=jn(a,e+2),n=Qr(t,$n(t),e+2);n||ut(!1,"ERR43: argument marshaler must be resolved"),u[e]=n}const d=jn(a,1);let f=$n(d);const _=20==f,m=26==f;_&&(f=30);const h=Dt(d,f,1),g={method:e,fullyQualifiedName:i,args_count:p,arg_marshalers:u,res_converter:h,is_async:_,is_discard_no_wait:m,isDisposed:!1};let b;b=_?1==p&&h?function(e){const t=e.method,n=e.arg_marshalers[0],r=e.res_converter,o=e.fullyQualifiedName;return e=null,function(e){const s=Bt();st.assert_runtime_running();const a=Xe.stackSave();try{const o=xn(3);n(o,e);let s=r(o);return hn(ot.managedThreadTID,t,o),s=nn(o,void 0,s),s}finally{Xe.stackRestore(a),Nt(s,"mono.callCsFunction:",o)}}}(g):2==p&&h?function(e){const t=e.method,n=e.arg_marshalers[0],r=e.arg_marshalers[1],o=e.res_converter,s=e.fullyQualifiedName;return e=null,function(e,a){const i=Bt();st.assert_runtime_running();const c=Xe.stackSave();try{const s=xn(4);n(s,e),r(s,a);let i=o(s);return hn(ot.managedThreadTID,t,s),i=nn(s,void 0,i),i}finally{Xe.stackRestore(c),Nt(i,"mono.callCsFunction:",s)}}}(g):gr(g):m?gr(g):0!=p||h?1!=p||h?1==p&&h?function(e){const t=e.method,n=e.arg_marshalers[0],r=e.res_converter,o=e.fullyQualifiedName;return e=null,function(e){const s=Bt();st.assert_runtime_running();const a=Xe.stackSave();try{const o=xn(3);return n(o,e),gn(t,o),r(o)}finally{Xe.stackRestore(a),Nt(s,"mono.callCsFunction:",o)}}}(g):2==p&&h?function(e){const t=e.method,n=e.arg_marshalers[0],r=e.arg_marshalers[1],o=e.res_converter,s=e.fullyQualifiedName;return e=null,function(e,a){const i=Bt();st.assert_runtime_running();const c=Xe.stackSave();try{const s=xn(4);return n(s,e),r(s,a),gn(t,s),o(s)}finally{Xe.stackRestore(c),Nt(i,"mono.callCsFunction:",s)}}}(g):gr(g):function(e){const t=e.method,n=e.arg_marshalers[0],r=e.fullyQualifiedName;return e=null,function(e){const o=Bt();st.assert_runtime_running();const s=Xe.stackSave();try{const r=xn(3);n(r,e),gn(t,r)}finally{Xe.stackRestore(s),Nt(o,"mono.callCsFunction:",r)}}}(g):function(e){const t=e.method,n=e.fullyQualifiedName;return e=null,function(){const e=Bt();st.assert_runtime_running();const r=Xe.stackSave();try{const e=xn(2);gn(t,e)}finally{Xe.stackRestore(r),Nt(e,"mono.callCsFunction:",n)}}}(g),b[kn]=g,function(e,t,n,r,o,s){const a=`${t}.${n}`.replace(/\//g,".").split(".");let i,c=br.get(e);c||(c={},br.set(e,c),br.set(e+".dll",c)),i=c;for(let e=0;e<a.length;e++){const t=a[e];if(""!=t){let e=i[t];void 0===e&&(e={},i[t]=e),e||ut(!1,`${t} not found while looking up ${n}`),i=e}}i[r]||(i[r]=s),i[`${r}.${o}`]=s}(t,n,r,o,s,b),Nt(c,"mono.bindCsFunction:",i)}function gr(e){const t=e.args_count,n=e.arg_marshalers,r=e.res_converter,o=e.method,s=e.fullyQualifiedName,a=e.is_async,i=e.is_discard_no_wait;return e=null,function(...e){const c=Bt();st.assert_runtime_running();const l=Xe.stackSave();try{const s=xn(2+t);for(let r=0;r<t;r++){const t=n[r];t&&t(s,e[r])}let c;return a&&(c=r(s)),a?(hn(ot.managedThreadTID,o,s),c=nn(s,void 0,c)):i?hn(ot.managedThreadTID,o,s):(gn(o,s),r&&(c=r(s))),c}finally{Xe.stackRestore(l),Nt(c,"mono.callCsFunction:",s)}}}const br=new Map;async function yr(e){return dr(),br.get(e)||await function(e){st.assert_runtime_running();const t=Xe.stackSave();try{const t=xn(3),n=In(t,1);po(In(t,2),e);let r=tn(n);return hn(ot.managedThreadTID,mn.BindAssemblyExports,t),r=nn(t,Ht,r),null==r&&(r=Promise.resolve()),r}finally{Xe.stackRestore(t)}}(e),br.get(e)||{}}const wr="function"==typeof globalThis.FinalizationRegistry;let kr;const Sr=[null],vr=[null],Ur=[];let Er=1;const Tr=new Map,xr=[];let Ir=-2;function Ar(e){return e<-1}function jr(e){return e>0}function $r(e){return e<-1}wr&&(kr=new globalThis.FinalizationRegistry(Pr));const Lr=Symbol.for("wasm js_owned_gc_handle"),Rr=Symbol.for("wasm cs_owned_js_handle"),Br=Symbol.for("wasm do_not_force_dispose");function Nr(e){return jr(e)?Sr[e]:Ar(e)?vr[0-e]:null}function Cr(e){if(dr(),e[Rr])return e[Rr];const t=Ur.length?Ur.pop():Er++;return Sr[t]=e,Object.isExtensible(e)&&(e[Rr]=t),t}function Or(e){let t;jr(e)?(t=Sr[e],Sr[e]=void 0,Ur.push(e)):Ar(e)&&(t=vr[0-e],vr[0-e]=void 0),null==t&&ut(!1,"ObjectDisposedException"),void 0!==t[Rr]&&(t[Rr]=void 0)}function Dr(e,t){dr(),e[Lr]=t,wr&&kr.register(e,t,e);const n=mr(e);Tr.set(t,n)}function Fr(e,t,r){var o;dr(),e&&(t=e[Lr],e[Lr]=p,wr&&kr.unregister(e)),t!==p&&Tr.delete(t)&&!r&&st.is_runtime_running()&&!zr&&function(e){e||ut(!1,"Must be valid gc_handle"),st.assert_runtime_running();const t=Xe.stackSave();try{const t=xn(3),r=In(t,2);Mn(r,14),Xn(r,e),n&&!$r(e)&&_n.isUI||gn(mn.ReleaseJSOwnedObjectByGCHandle,t)}finally{Xe.stackRestore(t)}}(t),$r(t)&&(o=t,xr.push(o))}function Mr(e){const t=e[Lr];if(t==p)throw new Error("Assert failed: ObjectDisposedException");return t}function Pr(e){st.is_runtime_running()&&Fr(null,e)}function Vr(e){if(!e)return null;const t=Tr.get(e);return t?t.deref():null}let zr=!1;function Hr(e,t){let n=!1,r=!1;zr=!0;let o=0,s=0,a=0,i=0;const c=[...Tr.keys()];for(const e of c){const r=Tr.get(e),o=r&&r.deref();if(wr&&o&&kr.unregister(o),o){const s="boolean"==typeof o[Br]&&o[Br];if(t&&Me(`Proxy of C# ${typeof o} with GCHandle ${e} was still alive. ${s?"keeping":"disposing"}.`),s)n=!0;else{const t=st.getPromiseController(o);t&&t.reject(new Error("WebWorker which is origin of the Task is being terminated.")),"function"==typeof o.dispose&&o.dispose(),o[Lr]===e&&(o[Lr]=p),!_r&&r&&r.dispose(),a++}}}n||(Tr.clear(),wr&&(kr=new globalThis.FinalizationRegistry(Pr)));const l=(e,n)=>{const o=n[e],s=o&&"boolean"==typeof o[Br]&&o[Br];if(s||(n[e]=void 0),o)if(t&&Me(`Proxy of JS ${typeof o} with JSHandle ${e} was still alive. ${s?"keeping":"disposing"}.`),s)r=!0;else{const t=st.getPromiseController(o);t&&t.reject(new Error("WebWorker which is origin of the Task is being terminated.")),"function"==typeof o.dispose&&o.dispose(),o[Rr]===e&&(o[Rr]=void 0),i++}};for(let e=0;e<Sr.length;e++)l(e,Sr);for(let e=0;e<vr.length;e++)l(e,vr);if(r||(Sr.length=1,vr.length=1,Er=1,Ur.length=0),xr.length=0,Ir=-2,e){for(const e of tr)if(e){const t=e[vn];t&&(t.disposed=!0,o++)}tr.length=1;const e=[...br.values()];for(const t of e)for(const e in t){const n=t[e][kn];n&&(n.disposed=!0,s++)}br.clear()}Fe(`forceDisposeProxies done: ${o} imports, ${s} exports, ${a} GCHandles, ${i} JSHandles.`)}function Wr(e){return Promise.resolve(e)===e||("object"==typeof e||"function"==typeof e)&&"function"==typeof e.then}function qr(e){const{promise:t,promise_control:n}=pt();return e().then((e=>n.resolve(e))).catch((e=>n.reject(e))),t}const Gr=Symbol.for("wasm promise_holder");class Jr extends ManagedObject{constructor(e,t,n,r){super(),this.promise=e,this.gc_handle=t,this.promiseHolderPtr=n,this.res_converter=r,this.isResolved=!1,this.isPosted=!1,this.isPostponed=!1,this.data=null,this.reason=void 0}setIsResolving(){return!0}resolve(e){st.is_runtime_running()?(this.isResolved&&ut(!1,"resolve could be called only once"),this.isDisposed&&ut(!1,"resolve is already disposed."),this.isResolved=!0,this.complete_task_wrapper(e,null)):st.diagnosticTracing&&De("This promise resolution can't be propagated to managed code, mono runtime already exited.")}reject(e){st.is_runtime_running()?(e||(e=new Error),this.isResolved&&ut(!1,"reject could be called only once"),this.isDisposed&&ut(!1,"resolve is already disposed."),e[Gr],this.isResolved=!0,this.complete_task_wrapper(null,e)):st.diagnosticTracing&&De("This promise rejection can't be propagated to managed code, mono runtime already exited.")}cancel(){if(st.is_runtime_running())if(this.isResolved&&ut(!1,"cancel could be called only once"),this.isDisposed&&ut(!1,"resolve is already disposed."),this.isPostponed)this.isResolved=!0,void 0!==this.reason?this.complete_task_wrapper(null,this.reason):this.complete_task_wrapper(this.data,null);else{const e=this.promise;st.assertIsControllablePromise(e);const t=st.getPromiseController(e),n=new Error("OperationCanceledException");n[Gr]=this,t.reject(n)}else st.diagnosticTracing&&De("This promise cancelation can't be propagated to managed code, mono runtime already exited.")}complete_task_wrapper(e,t){try{this.isPosted&&ut(!1,"Promise is already posted to managed."),this.isPosted=!0,Fr(this,this.gc_handle,!0),function(e,t,n,r){st.assert_runtime_running();const o=Xe.stackSave();try{const o=xn(5),s=In(o,2);Mn(s,14),Xn(s,e);const a=In(o,3);if(t)ho(a,t);else{Mn(a,0);const e=In(o,4);r||ut(!1,"res_converter missing"),r(e,n)}hn(ot.ioThreadTID,mn.CompleteTask,o)}finally{Xe.stackRestore(o)}}(this.gc_handle,t,e,this.res_converter||bo)}catch(e){try{st.mono_exit(1,e)}catch(e){}}}}const Xr="For more information see https://aka.ms/dotnet-wasm-jsinterop";function Qr(e,t,n){if(0===t||1===t||2===t||26===t)return;let r,o,s,a;o=Ft(Rn(e)),s=Ft(Bn(e)),a=Ft(Nn(e));const i=Ln(e);r=Yr(i),19===t&&(t=i);const c=Yr(t),l=Rn(e),p=n*Un;return(e,t)=>{c(e+p,t,l,r,o,s,a)}}function Yr(e){if(0===e||1===e)return;const t=wn.get(e);return t&&"function"==typeof t||ut(!1,`ERR30: Unknown converter for type ${e}`),t}function Zr(e,t){null==t?Mn(e,0):(Mn(e,3),Vn(e,t))}function Kr(e,t){null==t?Mn(e,0):(Mn(e,4),function(e,t){e||ut(!1,"Null arg"),g(e,t)}(e,t))}function eo(e,t){null==t?Mn(e,0):(Mn(e,5),function(e,t){e||ut(!1,"Null arg"),b(e,t)}(e,t))}function to(e,t){null==t?Mn(e,0):(Mn(e,6),function(e,t){e||ut(!1,"Null arg"),S(e,t)}(e,t))}function no(e,t){null==t?Mn(e,0):(Mn(e,7),function(e,t){e||ut(!1,"Null arg"),v(e,t)}(e,t))}function ro(e,t){null==t?Mn(e,0):(Mn(e,8),function(e,t){if(e||ut(!1,"Null arg"),!Number.isSafeInteger(t))throw new Error(`Assert failed: Value is not an integer: ${t} (${typeof t})`);A(e,t)}(e,t))}function oo(e,t){null==t?Mn(e,0):(Mn(e,9),function(e,t){e||ut(!1,"Null arg"),x(e,t)}(e,t))}function so(e,t){null==t?Mn(e,0):(Mn(e,10),Wn(e,t))}function ao(e,t){null==t?Mn(e,0):(Mn(e,11),function(e,t){e||ut(!1,"Null arg"),I(e,t)}(e,t))}function io(e,t){null==t?Mn(e,0):(Mn(e,12),zn(e,t))}function co(e,t){if(null==t)Mn(e,0);else{if(!(t instanceof Date))throw new Error("Assert failed: Value is not a Date");Mn(e,17),Hn(e,t)}}function lo(e,t){if(null==t)Mn(e,0);else{if(!(t instanceof Date))throw new Error("Assert failed: Value is not a Date");Mn(e,18),Hn(e,t)}}function po(e,t){if(null==t)Mn(e,0);else{if(Mn(e,15),"string"!=typeof t)throw new Error("Assert failed: Value is not a String");uo(e,t)}}function uo(e,t){{const n=Qn(e);try{!function(e,t){if(t.clear(),null!==e)if("symbol"==typeof e)Re(e,t);else{if("string"!=typeof e)throw new Error("Expected string argument, got "+typeof e);if(0===e.length)Re(e,t);else{if(e.length<=256){const n=_e.get(e);if(n)return void t.set(n)}Be(e,t)}}}(t,n)}finally{n.release()}}}function fo(e){Mn(e,0)}function _o(e,t,r,o,s,a,i){if(null==t)return void Mn(e,0);if(!(t&&t instanceof Function))throw new Error("Assert failed: Value is not a Function");const c=function(e){const r=In(e,0),l=In(e,1),p=In(e,2),u=In(e,3),d=In(e,4),f=ot.isPendingSynchronousCall;try{let e,r,f;n&&c.isDisposed,s&&(e=s(p)),a&&(r=a(u)),i&&(f=i(d)),ot.isPendingSynchronousCall=!0;const _=t(e,r,f);o&&o(l,_)}catch(e){ho(r,e)}finally{ot.isPendingSynchronousCall=f}};c[Sn]=!0,c.isDisposed=!1,c.dispose=()=>{c.isDisposed=!0},Gn(e,Cr(c)),Mn(e,25)}function mo(e,t,n,r){const o=30==Dn(e);if(null==t)return void Mn(e,0);if(!Wr(t))throw new Error("Assert failed: Value is not a Promise");const s=o?Jn(e):xr.length?xr.pop():Ir--;o||(Xn(e,s),Mn(e,20));const a=new Jr(t,s,0,r);Dr(a,s),t.then((e=>a.resolve(e)),(e=>a.reject(e)))}function ho(e,t){if(null==t)Mn(e,0);else if(t instanceof ManagedError)Mn(e,16),Xn(e,Mr(t));else{if("object"!=typeof t&&"string"!=typeof t)throw new Error("Assert failed: Value is not an Error "+typeof t);Mn(e,27),uo(e,t.toString());const n=t[Rr];Gn(e,n||Cr(t))}}function go(e,t){if(null==t)Mn(e,0);else{if(void 0!==t[Lr])throw new Error(`Assert failed: JSObject proxy of ManagedObject proxy is not supported. ${Xr}`);if("function"!=typeof t&&"object"!=typeof t)throw new Error(`Assert failed: JSObject proxy of ${typeof t} is not supported`);Mn(e,13),Gn(e,Cr(t))}}function bo(e,t){if(null==t)Mn(e,0);else{const n=t[Lr],r=typeof t;if(void 0===n)if("string"===r||"symbol"===r)Mn(e,15),uo(e,t);else if("number"===r)Mn(e,10),Wn(e,t);else{if("bigint"===r)throw new Error("NotImplementedException: bigint");if("boolean"===r)Mn(e,3),Vn(e,t);else if(t instanceof Date)Mn(e,17),Hn(e,t);else if(t instanceof Error)ho(e,t);else if(t instanceof Uint8Array)wo(e,t,4);else if(t instanceof Float64Array)wo(e,t,10);else if(t instanceof Int32Array)wo(e,t,7);else if(Array.isArray(t))wo(e,t,14);else{if(t instanceof Int16Array||t instanceof Int8Array||t instanceof Uint8ClampedArray||t instanceof Uint16Array||t instanceof Uint32Array||t instanceof Float32Array)throw new Error("NotImplementedException: TypedArray");if(Wr(t))mo(e,t);else{if(t instanceof Span)throw new Error("NotImplementedException: Span");if("object"!=r)throw new Error(`JSObject proxy is not supported for ${r} ${t}`);{const n=Cr(t);Mn(e,13),Gn(e,n)}}}}else{if(Mr(t),t instanceof ArraySegment)throw new Error("NotImplementedException: ArraySegment. "+Xr);if(t instanceof ManagedError)Mn(e,16),Xn(e,n);else{if(!(t instanceof ManagedObject))throw new Error("NotImplementedException "+r+". "+Xr);Mn(e,14),Xn(e,n)}}}}function yo(e,t,n){n||ut(!1,"Expected valid element_type parameter"),wo(e,t,n)}function wo(e,t,n){if(null==t)Mn(e,0);else{const r=Kn(n);-1==r&&ut(!1,`Element type ${n} not supported`);const s=t.length,a=r*s,i=Xe._malloc(a);if(15==n){if(!Array.isArray(t))throw new Error("Assert failed: Value is not an Array");_(i,a),o.mono_wasm_register_root(i,a,"marshal_array_to_cs");for(let e=0;e<s;e++)po(In(i,e),t[e])}else if(14==n){if(!Array.isArray(t))throw new Error("Assert failed: Value is not an Array");_(i,a),o.mono_wasm_register_root(i,a,"marshal_array_to_cs");for(let e=0;e<s;e++)bo(In(i,e),t[e])}else if(13==n){if(!Array.isArray(t))throw new Error("Assert failed: Value is not an Array");_(i,a);for(let e=0;e<s;e++)go(In(i,e),t[e])}else if(4==n){if(!(Array.isArray(t)||t instanceof Uint8Array))throw new Error("Assert failed: Value is not an Array or Uint8Array");Y().subarray(i,i+s).set(t)}else if(7==n){if(!(Array.isArray(t)||t instanceof Int32Array))throw new Error("Assert failed: Value is not an Array or Int32Array");X().subarray(i>>2,(i>>2)+s).set(t)}else{if(10!=n)throw new Error("not implemented");if(!(Array.isArray(t)||t instanceof Float64Array))throw new Error("Assert failed: Value is not an Array or Float64Array");te().subarray(i>>3,(i>>3)+s).set(t)}zn(e,i),Mn(e,21),function(e,t){e||ut(!1,"Null arg"),g(e+13,t)}(e,n),Zn(e,t.length)}}function ko(e,t,n){if(n||ut(!1,"Expected valid element_type parameter"),t.isDisposed)throw new Error("Assert failed: ObjectDisposedException");vo(n,t._viewType),Mn(e,23),zn(e,t._pointer),Zn(e,t.length)}function So(e,t,n){n||ut(!1,"Expected valid element_type parameter");const r=Mr(t);r||ut(!1,"Only roundtrip of ArraySegment instance created by C#"),vo(n,t._viewType),Mn(e,22),zn(e,t._pointer),Zn(e,t.length),Xn(e,r)}function vo(e,t){if(4==e){if(0!=t)throw new Error("Assert failed: Expected MemoryViewType.Byte")}else if(7==e){if(1!=t)throw new Error("Assert failed: Expected MemoryViewType.Int32")}else{if(10!=e)throw new Error(`NotImplementedException ${e} `);if(2!=t)throw new Error("Assert failed: Expected MemoryViewType.Double")}}const Uo={now:function(){return Date.now()}};function Eo(e){void 0===globalThis.performance&&(globalThis.performance=Uo),e.require=Qe.require,e.scriptDirectory=st.scriptDirectory,Xe.locateFile===Xe.__locateFile&&(Xe.locateFile=st.locateFile),e.fetch=st.fetch_like,e.ENVIRONMENT_IS_WORKER=et}function To(){if("function"!=typeof globalThis.fetch||"function"!=typeof globalThis.AbortController)throw new Error(Ye?"Please install `node-fetch` and `node-abort-controller` npm packages to enable HTTP client support. See also https://aka.ms/dotnet-wasm-features":"This browser doesn't support fetch API. Please use a modern browser. See also https://aka.ms/dotnet-wasm-features")}let xo,Io;function Ao(){if(void 0!==xo)return xo;if("undefined"!=typeof Request&&"body"in Request.prototype&&"function"==typeof ReadableStream&&"function"==typeof TransformStream){let e=!1;const t=new Request("",{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");xo=e&&!t}else xo=!1;return xo}function jo(){return void 0!==Io||(Io="undefined"!=typeof Response&&"body"in Response.prototype&&"function"==typeof ReadableStream),Io}function $o(){return To(),dr(),{abortController:new AbortController}}function Lo(e){e.catch((e=>{e&&"AbortError"!==e&&"AbortError"!==e.name&&Xe.err("Unexpected error: "+e)}))}function Ro(e){try{e.isAborted||(e.streamWriter&&(Lo(e.streamWriter.abort()),e.isAborted=!0),e.streamReader&&(Lo(e.streamReader.cancel()),e.isAborted=!0)),e.isAborted||e.abortController.abort("AbortError")}catch(e){}}function Bo(e,t,n){n>0||ut(!1,"expected bufferLength > 0");const r=new Span(t,n,0).slice();return qr((async()=>{e.streamWriter||ut(!1,"expected streamWriter"),e.responsePromise||ut(!1,"expected fetch promise");try{await e.streamWriter.ready,await e.streamWriter.write(r)}catch(e){throw new Error("BrowserHttpWriteStream.Rejected")}}))}function No(e){return e||ut(!1,"expected controller"),qr((async()=>{e.streamWriter||ut(!1,"expected streamWriter"),e.responsePromise||ut(!1,"expected fetch promise");try{await e.streamWriter.ready,await e.streamWriter.close()}catch(e){throw new Error("BrowserHttpWriteStream.Rejected")}}))}function Co(e,t,n,r,o,s){const a=new TransformStream;return e.streamWriter=a.writable.getWriter(),Lo(e.streamWriter.closed),Lo(e.streamWriter.ready),Do(e,t,n,r,o,s,a.readable)}function Oo(e,t,n,r,o,s,a,i){return Do(e,t,n,r,o,s,new Span(a,i,0).slice())}function Do(e,t,n,r,o,s,a){To(),dr(),t&&"string"==typeof t||ut(!1,"expected url string"),n&&r&&Array.isArray(n)&&Array.isArray(r)&&n.length===r.length||ut(!1,"expected headerNames and headerValues arrays"),o&&s&&Array.isArray(o)&&Array.isArray(s)&&o.length===s.length||ut(!1,"expected headerNames and headerValues arrays");const i=new Headers;for(let e=0;e<n.length;e++)i.append(n[e],r[e]);const c={body:a,headers:i,signal:e.abortController.signal};"undefined"!=typeof ReadableStream&&a instanceof ReadableStream&&(c.duplex="half");for(let e=0;e<o.length;e++)c[o[e]]=s[e];return e.responsePromise=qr((()=>st.fetch_like(t,c))),e.responsePromise.then((t=>{if(e.response=t,e.responseHeaderNames=[],e.responseHeaderValues=[],t.headers&&t.headers.entries){const n=t.headers.entries();for(const t of n)e.responseHeaderNames.push(t[0]),e.responseHeaderValues.push(t[1])}})).catch((()=>{})),e.responsePromise}function Fo(e){var t;return null===(t=e.response)||void 0===t?void 0:t.type}function Mo(e){var t,n;return null!==(n=null===(t=e.response)||void 0===t?void 0:t.status)&&void 0!==n?n:0}function Po(e){return e.responseHeaderNames||ut(!1,"expected responseHeaderNames"),e.responseHeaderNames}function Vo(e){return e.responseHeaderValues||ut(!1,"expected responseHeaderValues"),e.responseHeaderValues}function zo(e){return qr((async()=>{const t=await e.response.arrayBuffer();return e.responseBuffer=t,e.currentBufferOffset=0,t.byteLength}))}function Ho(e,t){if(e||ut(!1,"expected controller"),e.responseBuffer||ut(!1,"expected resoved arrayBuffer"),null==e.currentBufferOffset&&ut(!1,"expected currentBufferOffset"),e.currentBufferOffset==e.responseBuffer.byteLength)return 0;const n=new Uint8Array(e.responseBuffer,e.currentBufferOffset);t.set(n,0);const r=Math.min(t.byteLength,n.byteLength);return e.currentBufferOffset+=r,r}function Wo(e,t,n){const r=new Span(t,n,0);return qr((async()=>{if(e.response||ut(!1,"expected response"),e.streamReader||(e.streamReader=e.response.body.getReader()),e.currentStreamReaderChunk&&void 0!==e.currentBufferOffset||(e.currentStreamReaderChunk=await e.streamReader.read(),e.currentBufferOffset=0),e.currentStreamReaderChunk.done){if(e.isAborted)throw new Error("OperationCanceledException");return 0}const t=e.currentStreamReaderChunk.value.byteLength-e.currentBufferOffset;t>0||ut(!1,"expected remaining_source to be greater than 0");const n=Math.min(t,r.byteLength),o=e.currentStreamReaderChunk.value.subarray(e.currentBufferOffset,e.currentBufferOffset+n);return r.set(o,0),e.currentBufferOffset+=n,t==n&&(e.currentStreamReaderChunk=void 0),n}))}let qo,Go=0,Jo=0;function Xo(){if(!st.isChromium)return;const e=(new Date).valueOf(),t=e+36e4;for(let n=Math.max(e+1e3,Go);n<t;n+=1e3){const t=n-e;globalThis.setTimeout(Qo,t)}Go=t}function Qo(){if(Xe.maybeExit(),st.is_runtime_running()){try{o.mono_wasm_execute_timer(),Jo++}catch(e){st.mono_exit(1,e)}Yo()}}function Yo(){Xe.maybeExit();try{for(;Jo>0;){if(--Jo,!st.is_runtime_running())return;o.mono_background_exec()}}catch(e){st.mono_exit(1,e)}}function mono_wasm_schedule_timer_tick(){if(Xe.maybeExit(),st.is_runtime_running()){qo=void 0;try{o.mono_wasm_execute_timer(),Jo++}catch(e){st.mono_exit(1,e)}}}class Zo{constructor(){this.queue=[],this.offset=0}getLength(){return this.queue.length-this.offset}isEmpty(){return 0==this.queue.length}enqueue(e){this.queue.push(e)}dequeue(){if(0===this.queue.length)return;const e=this.queue[this.offset];return this.queue[this.offset]=null,2*++this.offset>=this.queue.length&&(this.queue=this.queue.slice(this.offset),this.offset=0),e}peek(){return this.queue.length>0?this.queue[this.offset]:void 0}drain(e){for(;this.getLength();)e(this.dequeue())}}const Ko=Symbol.for("wasm ws_pending_send_buffer"),es=Symbol.for("wasm ws_pending_send_buffer_offset"),ts=Symbol.for("wasm ws_pending_send_buffer_type"),ns=Symbol.for("wasm ws_pending_receive_event_queue"),rs=Symbol.for("wasm ws_pending_receive_promise_queue"),os=Symbol.for("wasm ws_pending_open_promise"),ss=Symbol.for("wasm wasm_ws_pending_open_promise_used"),as=Symbol.for("wasm wasm_ws_pending_error"),is=Symbol.for("wasm ws_pending_close_promises"),cs=Symbol.for("wasm ws_pending_send_promises"),ls=Symbol.for("wasm ws_is_aborted"),ps=Symbol.for("wasm wasm_ws_close_sent"),us=Symbol.for("wasm wasm_ws_close_received"),ds=Symbol.for("wasm ws_receive_status_ptr"),fs=65536,_s=new Uint8Array;function ms(e){var t,n;return e.readyState!=WebSocket.CLOSED?null!==(t=e.readyState)&&void 0!==t?t:-1:0==e[ns].getLength()?null!==(n=e.readyState)&&void 0!==n?n:-1:WebSocket.OPEN}function hs(e,t,n){let r;!function(){if(nt)throw new Error("WebSockets are not supported in shell JS engine.");if("function"!=typeof globalThis.WebSocket)throw new Error(Ye?"Please install `ws` npm package to enable networking support. See also https://aka.ms/dotnet-wasm-features":"This browser doesn't support WebSocket API. Please use a modern browser. See also https://aka.ms/dotnet-wasm-features")}(),dr(),e&&"string"==typeof e||ut(!1,"ERR12: Invalid uri "+typeof e);try{r=new globalThis.WebSocket(e,t||void 0)}catch(e){throw Me("WebSocket error in ws_wasm_create: "+e.toString()),e}const{promise_control:o}=pt();r[ns]=new Zo,r[rs]=new Zo,r[os]=o,r[cs]=[],r[is]=[],r[ds]=n,r.binaryType="arraybuffer";const s=()=>{try{if(r[ls])return;if(!st.is_runtime_running())return;o.resolve(r),Xo()}catch(e){Me("failed to propagate WebSocket open event: "+e.toString())}},a=e=>{try{if(r[ls])return;if(!st.is_runtime_running())return;!function(e,t){const n=e[ns],r=e[rs];if("string"==typeof t.data)n.enqueue({type:0,data:Te(t.data),offset:0});else{if("ArrayBuffer"!==t.data.constructor.name)throw new Error("ERR19: WebSocket receive expected ArrayBuffer");n.enqueue({type:1,data:new Uint8Array(t.data),offset:0})}if(r.getLength()&&n.getLength()>1)throw new Error("ERR21: Invalid WS state");for(;r.getLength()&&n.getLength();){const t=r.dequeue();vs(e,n,t.buffer_ptr,t.buffer_length),t.resolve()}Xo()}(r,e),Xo()}catch(e){Me("failed to propagate WebSocket message event: "+e.toString())}},i=e=>{try{if(r.removeEventListener("message",a),r[ls])return;if(!st.is_runtime_running())return;r[us]=!0,r.close_status=e.code,r.close_status_description=e.reason,r[ss]&&o.reject(new Error(e.reason));for(const e of r[is])e.resolve();r[rs].drain((e=>{v(n,0),v(n+4,2),v(n+8,1),e.resolve()}))}catch(e){Me("failed to propagate WebSocket close event: "+e.toString())}},c=e=>{try{if(r[ls])return;if(!st.is_runtime_running())return;r.removeEventListener("message",a);const t=e.message?"WebSocket error: "+e.message:"WebSocket error";Me(t),r[as]=t,Ss(r,new Error(t))}catch(e){Me("failed to propagate WebSocket error event: "+e.toString())}};return r.addEventListener("message",a),r.addEventListener("open",s,{once:!0}),r.addEventListener("close",i,{once:!0}),r.addEventListener("error",c,{once:!0}),r.dispose=()=>{r.removeEventListener("message",a),r.removeEventListener("open",s),r.removeEventListener("close",i),r.removeEventListener("error",c),ks(r)},r}function gs(e){if(e||ut(!1,"ERR17: expected ws instance"),e[as])return Us(e[as]);const t=e[os];return e[ss]=!0,t.promise}function bs(e,t,n,r,o){if(e||ut(!1,"ERR17: expected ws instance"),e[as])return Us(e[as]);if(e[ls]||e[ps])return Us("InvalidState: The WebSocket is not connected.");if(e.readyState==WebSocket.CLOSED)return null;const s=function(e,t,n,r){let o=e[Ko],s=0;const a=t.byteLength;if(o){if(s=e[es],n=e[ts],0!==a){if(s+a>o.length){const n=new Uint8Array(1.5*(s+a+50));n.set(o,0),n.subarray(s).set(t),e[Ko]=o=n}else o.subarray(s).set(t);s+=a,e[es]=s}}else r?0!==a&&(o=t,s=a):(0!==a&&(o=t.slice(),s=a,e[es]=s,e[Ko]=o),e[ts]=n);return r?0==s||null==o?_s:0===n?function(e){return void 0===ye?Xe.UTF8ArrayToString(e,0,e.byteLength):ye.decode(e)}(Ne(o,0,s)):o.subarray(0,s):null}(e,new Uint8Array(Y().buffer,t,n),r,o);return o&&s?function(e,t){if(e.send(t),e[Ko]=null,e.bufferedAmount<fs)return null;const{promise:n,promise_control:r}=pt(),o=e[cs];o.push(r);let s=1;const a=()=>{try{if(0===e.bufferedAmount)r.resolve();else{const t=e.readyState;if(t!=WebSocket.OPEN&&t!=WebSocket.CLOSING)r.reject(new Error(`InvalidState: ${t} The WebSocket is not connected.`));else if(!r.isDone)return globalThis.setTimeout(a,s),void(s=Math.min(1.5*s,1e3))}const t=o.indexOf(r);t>-1&&o.splice(t,1)}catch(e){Me("WebSocket error in web_socket_send_and_wait: "+e.toString()),r.reject(e)}};return globalThis.setTimeout(a,0),n}(e,s):null}function ys(e,t,n){if(e||ut(!1,"ERR18: expected ws instance"),e[as])return Us(e[as]);if(e[ls]){const t=e[ds];return v(t,0),v(t+4,2),v(t+8,1),null}const r=e[ns],o=e[rs];if(r.getLength())return 0!=o.getLength()&&ut(!1,"ERR20: Invalid WS state"),vs(e,r,t,n),null;if(e[us]){const t=e[ds];return v(t,0),v(t+4,2),v(t+8,1),null}const{promise:s,promise_control:a}=pt(),i=a;return i.buffer_ptr=t,i.buffer_length=n,o.enqueue(i),s}function ws(e,t,n,r){if(e||ut(!1,"ERR19: expected ws instance"),e[ls]||e[ps]||e.readyState==WebSocket.CLOSED)return null;if(e[as])return Us(e[as]);if(e[ps]=!0,r){const{promise:r,promise_control:o}=pt();return e[is].push(o),"string"==typeof n?e.close(t,n):e.close(t),r}return"string"==typeof n?e.close(t,n):e.close(t),null}function ks(e){if(e||ut(!1,"ERR18: expected ws instance"),!e[ls]&&!e[ps]){e[ls]=!0,Ss(e,new Error("OperationCanceledException"));try{e.close(1e3,"Connection was aborted.")}catch(e){Me("WebSocket error in ws_wasm_abort: "+e.toString())}}}function Ss(e,t){const n=e[os],r=e[ss];n&&r&&n.reject(t);for(const n of e[is])n.reject(t);for(const n of e[cs])n.reject(t);e[rs].drain((e=>{e.reject(t)}))}function vs(e,t,n,r){const o=t.peek(),s=Math.min(r,o.data.length-o.offset);if(s>0){const e=o.data.subarray(o.offset,o.offset+s);new Uint8Array(Y().buffer,n,r).set(e,0),o.offset+=s}const a=o.data.length===o.offset?1:0;a&&t.dequeue();const i=e[ds];v(i,s),v(i+4,o.type),v(i+8,a)}function Us(e){return function(e){const{promise:t,promise_control:n}=pt();return e.then((e=>n.resolve(e))).catch((e=>n.reject(e))),t}(Promise.reject(new Error(e)))}function Es(e,t,n){st.diagnosticTracing&&De(`Loaded:${e.name} as ${e.behavior} size ${n.length} from ${t}`);const r=Bt(),s="string"==typeof e.virtualPath?e.virtualPath:e.name;let a=null;switch(e.behavior){case"dotnetwasm":case"js-module-threads":case"js-module-globalization":case"symbols":case"segmentation-rules":break;case"resource":case"assembly":case"pdb":st._loaded_files.push({url:t,file:s});case"heap":case"icu":a=function(e){const t=e.length+16;let n=Xe._sbrk(t);if(n<=0){if(n=Xe._sbrk(t),n<=0)throw Pe(`sbrk failed to allocate ${t} bytes, and failed upon retry.`),new Error("Out of memory");Me(`sbrk failed to allocate ${t} bytes, but succeeded upon retry!`)}return new Uint8Array(Y().buffer,n,e.length).set(e),n}(n);break;case"vfs":{const e=s.lastIndexOf("/");let t=e>0?s.substring(0,e):null,r=e>0?s.substring(e+1):s;r.startsWith("/")&&(r=r.substring(1)),t?(t.startsWith("/")||(t="/"+t),De(`Creating directory '${t}'`),Xe.FS_createPath("/",t,!0,!0)):t="/",st.diagnosticTracing&&De(`Creating file '${r}' in directory '${t}'`),Xe.FS_createDataFile(t,r,n,!0,!0,!0);break}default:throw new Error(`Unrecognized asset behavior:${e.behavior}, for asset ${e.name}`)}if("assembly"===e.behavior){if(!o.mono_wasm_add_assembly(s,a,n.length)){const e=st._loaded_files.findIndex((e=>e.file==s));st._loaded_files.splice(e,1)}}else"pdb"===e.behavior?o.mono_wasm_add_assembly(s,a,n.length):"icu"===e.behavior?function(e){if(!o.mono_wasm_load_icu_data(e))throw new Error("Failed to load ICU data")}(a):"resource"===e.behavior&&o.mono_wasm_add_satellite_assembly(s,e.culture||"",a,n.length);Nt(r,"mono.instantiateAsset:",e.name),++st.actual_instantiated_assets_count}async function Ts(e){try{const n=await e.pendingDownloadInternal.response;t=await n.text(),ze&&ut(!1,"Another symbol map was already loaded"),ze=t,st.diagnosticTracing&&De(`Deferred loading of ${t.length}ch symbol map`)}catch(t){Fe(`Error loading symbol file ${e.name}: ${JSON.stringify(t)}`)}var t}async function xs(e){try{const t=await e.pendingDownloadInternal.response,n=await t.json();at.setSegmentationRulesFromJson(n)}catch(t){Fe(`Error loading static json asset ${e.name}: ${JSON.stringify(t)}`)}}function Is(){return st.loadedFiles}const As={};function js(e){let t=As[e];if("string"!=typeof t){const n=o.mono_jiterp_get_opcode_info(e,0);As[e]=t=xe(n)}return t}const $s=2,Ls=64,Rs=64,Bs={};class Ns{constructor(e){this.locals=new Map,this.permanentFunctionTypeCount=0,this.permanentFunctionTypes={},this.permanentFunctionTypesByShape={},this.permanentFunctionTypesByIndex={},this.functionTypesByIndex={},this.permanentImportedFunctionCount=0,this.permanentImportedFunctions={},this.nextImportIndex=0,this.functions=[],this.estimatedExportBytes=0,this.frame=0,this.traceBuf=[],this.branchTargets=new Set,this.constantSlots=[],this.backBranchOffsets=[],this.callHandlerReturnAddresses=[],this.nextConstantSlot=0,this.backBranchTraceLevel=0,this.compressImportNames=!1,this.lockImports=!1,this._assignParameterIndices=e=>{let t=0;for(const n in e)this.locals.set(n,t),t++;return t},this.stack=[new Cs],this.clear(e),this.cfg=new Os(this),this.defineType("__cpp_exception",{ptr:127},64,!0)}clear(e){this.options=pa(),this.stackSize=1,this.inSection=!1,this.inFunction=!1,this.lockImports=!1,this.locals.clear(),this.functionTypeCount=this.permanentFunctionTypeCount,this.functionTypes=Object.create(this.permanentFunctionTypes),this.functionTypesByShape=Object.create(this.permanentFunctionTypesByShape),this.functionTypesByIndex=Object.create(this.permanentFunctionTypesByIndex),this.nextImportIndex=0,this.importedFunctionCount=0,this.importedFunctions=Object.create(this.permanentImportedFunctions);for(const e in this.importedFunctions)this.importedFunctions[e].index=void 0;this.functions.length=0,this.estimatedExportBytes=0,this.argumentCount=0,this.current.clear(),this.traceBuf.length=0,this.branchTargets.clear(),this.activeBlocks=0,this.nextConstantSlot=0,this.constantSlots.length=this.options.useConstants?e:0;for(let e=0;e<this.constantSlots.length;e++)this.constantSlots[e]=0;this.backBranchOffsets.length=0,this.callHandlerReturnAddresses.length=0,this.allowNullCheckOptimization=this.options.eliminateNullChecks,this.containsSimd=!1,this.containsAtomics=!1}_push(){this.stackSize++,this.stackSize>=this.stack.length&&this.stack.push(new Cs),this.current.clear()}_pop(e){if(this.stackSize<=1)throw new Error("Stack empty");const t=this.current;return this.stackSize--,e?(this.appendULeb(t.size),t.copyTo(this.current),null):t.getArrayView(!1).slice(0,t.size)}setImportFunction(e,t){const n=this.importedFunctions[e];if(!n)throw new Error("No import named "+e);n.func=t}getExceptionTag(){const e=Xe.wasmExports.__cpp_exception;return void 0!==e&&(e instanceof WebAssembly.Tag||ut(!1,`expected __cpp_exception export from dotnet.wasm to be WebAssembly.Tag but was ${e}`)),e}getWasmImports(){const e=ot.getMemory();e instanceof WebAssembly.Memory||ut(!1,`expected heap import to be WebAssembly.Memory but was ${e}`);const t=this.getExceptionTag(),n={c:this.getConstants(),m:{h:e}};t&&(n.x={e:t});const r=this.getImportsToEmit();for(let e=0;e<r.length;e++){const t=r[e];if("function"!=typeof t.func)throw new Error(`Import '${t.name}' not found or not a function`);const o=this.getCompressedName(t);let s=n[t.module];s||(s=n[t.module]={}),s[o]=t.func}return n}get bytesGeneratedSoFar(){const e=this.compressImportNames?8:20;return this.stack[0].size+32+this.importedFunctionCount*e+2*this.functions.length+this.estimatedExportBytes}get current(){return this.stack[this.stackSize-1]}get size(){return this.current.size}appendU8(e){if(e!=e>>>0||e>255)throw new Error(`Byte out of range: ${e}`);return this.current.appendU8(e)}appendSimd(e,t){return this.current.appendU8(253),0|e||0===e&&!0===t||ut(!1,"Expected non-v128_load simd opcode or allowLoad==true"),this.current.appendULeb(e)}appendAtomic(e,t){return this.current.appendU8(254),0|e||0===e&&!0===t||ut(!1,"Expected non-notify atomic opcode or allowNotify==true"),this.current.appendU8(e)}appendU32(e){return this.current.appendU32(e)}appendF32(e){return this.current.appendF32(e)}appendF64(e){return this.current.appendF64(e)}appendBoundaryValue(e,t){return this.current.appendBoundaryValue(e,t)}appendULeb(e){return this.current.appendULeb(e)}appendLeb(e){return this.current.appendLeb(e)}appendLebRef(e,t){return this.current.appendLebRef(e,t)}appendBytes(e){return this.current.appendBytes(e)}appendName(e){return this.current.appendName(e)}ret(e){this.ip_const(e),this.appendU8(15)}i32_const(e){this.appendU8(65),this.appendLeb(e)}ptr_const(e){let t=this.options.useConstants?this.constantSlots.indexOf(e):-1;this.options.useConstants&&t<0&&this.nextConstantSlot<this.constantSlots.length&&(t=this.nextConstantSlot++,this.constantSlots[t]=e),t>=0?(this.appendU8(35),this.appendLeb(t)):this.i32_const(e)}ip_const(e){this.appendU8(65),this.appendLeb(e-this.base)}i52_const(e){this.appendU8(66),this.appendLeb(e)}v128_const(e){if(0===e)this.local("v128_zero");else{if("object"!=typeof e)throw new Error("Expected v128_const arg to be 0 or a Uint8Array");{16!==e.byteLength&&ut(!1,"Expected v128_const arg to be 16 bytes in size");let t=!0;for(let n=0;n<16;n++)0!==e[n]&&(t=!1);t?this.local("v128_zero"):(this.appendSimd(12),this.appendBytes(e))}}}defineType(e,t,n,r){if(this.functionTypes[e])throw new Error(`Function type ${e} already defined`);if(r&&this.functionTypeCount>this.permanentFunctionTypeCount)throw new Error("New permanent function types cannot be defined after non-permanent ones");let o="";for(const e in t)o+=t[e]+",";o+=n;let s=this.functionTypesByShape[o];"number"!=typeof s&&(s=this.functionTypeCount++,r?(this.permanentFunctionTypeCount++,this.permanentFunctionTypesByShape[o]=s,this.permanentFunctionTypesByIndex[s]=[t,Object.values(t).length,n]):(this.functionTypesByShape[o]=s,this.functionTypesByIndex[s]=[t,Object.values(t).length,n]));const a=[s,t,n,`(${JSON.stringify(t)}) -> ${n}`,r];return r?this.permanentFunctionTypes[e]=a:this.functionTypes[e]=a,s}generateTypeSection(){this.beginSection(1),this.appendULeb(this.functionTypeCount);for(let e=0;e<this.functionTypeCount;e++){const t=this.functionTypesByIndex[e][0],n=this.functionTypesByIndex[e][1],r=this.functionTypesByIndex[e][2];this.appendU8(96),this.appendULeb(n);for(const e in t)this.appendU8(t[e]);64!==r?(this.appendULeb(1),this.appendU8(r)):this.appendULeb(0)}this.endSection()}getImportedFunctionTable(){const e={};for(const t in this.importedFunctions){const n=this.importedFunctions[t];e[this.getCompressedName(n)]=n.func}return e}getCompressedName(e){if(!this.compressImportNames||"number"!=typeof e.index)return e.name;let t=Bs[e.index];return"string"!=typeof t&&(Bs[e.index]=t=e.index.toString(36)),t}getImportsToEmit(){const e=[];for(const t in this.importedFunctions){const n=this.importedFunctions[t];"number"==typeof n.index&&e.push(n)}return e.sort(((e,t)=>e.index-t.index)),e}_generateImportSection(e){const t=this.getImportsToEmit();if(this.lockImports=!0,!1!==e)throw new Error("function table imports are disabled");const n=void 0!==this.getExceptionTag();this.beginSection(2),this.appendULeb(1+(n?1:0)+t.length+this.constantSlots.length+(!1!==e?1:0));for(let e=0;e<t.length;e++){const n=t[e];this.appendName(n.module),this.appendName(this.getCompressedName(n)),this.appendU8(0),this.appendU8(n.typeIndex)}for(let e=0;e<this.constantSlots.length;e++)this.appendName("c"),this.appendName(e.toString(36)),this.appendU8(3),this.appendU8(127),this.appendU8(0);this.appendName("m"),this.appendName("h"),this.appendU8(2),this.appendU8(0),this.appendULeb(1),n&&(this.appendName("x"),this.appendName("e"),this.appendU8(4),this.appendU8(0),this.appendULeb(this.getTypeIndex("__cpp_exception"))),!1!==e&&(this.appendName("f"),this.appendName("f"),this.appendU8(1),this.appendU8(112),this.appendU8(0),this.appendULeb(1))}defineImportedFunction(e,t,n,r,o){if(this.lockImports)throw new Error("Import section already generated");if(r&&this.importedFunctionCount>0)throw new Error("New permanent imports cannot be defined after any indexes have been assigned");const s=this.functionTypes[n];if(!s)throw new Error("No function type named "+n);if(r&&!s[4])throw new Error("A permanent import must have a permanent function type");const a=s[0],i=r?this.permanentImportedFunctions:this.importedFunctions;if("number"==typeof o&&(o=zs().get(o)),"function"!=typeof o&&void 0!==o)throw new Error(`Value passed for imported function ${t} was not a function or valid function pointer or undefined`);return i[t]={index:void 0,typeIndex:a,module:e,name:t,func:o}}markImportAsUsed(e){const t=this.importedFunctions[e];if(!t)throw new Error("No imported function named "+e);"number"!=typeof t.index&&(t.index=this.importedFunctionCount++)}getTypeIndex(e){const t=this.functionTypes[e];if(!t)throw new Error("No type named "+e);return t[0]}defineFunction(e,t){const n={index:this.functions.length,name:e.name,typeName:e.type,typeIndex:this.getTypeIndex(e.type),export:e.export,locals:e.locals,generator:t,error:null,blob:null};return this.functions.push(n),n.export&&(this.estimatedExportBytes+=n.name.length+8),n}emitImportsAndFunctions(e){let t=0;for(let e=0;e<this.functions.length;e++){const n=this.functions[e];n.export&&t++,this.beginFunction(n.typeName,n.locals);try{n.blob=n.generator()}finally{try{n.blob||(n.blob=this.endFunction(!1))}catch(e){}}}this._generateImportSection(e),this.beginSection(3),this.appendULeb(this.functions.length);for(let e=0;e<this.functions.length;e++)this.appendULeb(this.functions[e].typeIndex);this.beginSection(7),this.appendULeb(t);for(let e=0;e<this.functions.length;e++){const t=this.functions[e];t.export&&(this.appendName(t.name),this.appendU8(0),this.appendULeb(this.importedFunctionCount+e))}this.beginSection(10),this.appendULeb(this.functions.length);for(let e=0;e<this.functions.length;e++){const t=this.functions[e];t.blob||ut(!1,`expected function ${t.name} to have a body`),this.appendULeb(t.blob.length),this.appendBytes(t.blob)}this.endSection()}call_indirect(){throw new Error("call_indirect unavailable")}callImport(e){const t=this.importedFunctions[e];if(!t)throw new Error("No imported function named "+e);if("number"!=typeof t.index){if(this.lockImports)throw new Error("Import section was emitted before assigning an index to import named "+e);t.index=this.importedFunctionCount++}this.appendU8(16),this.appendULeb(t.index)}beginSection(e){this.inSection&&this._pop(!0),this.appendU8(e),this._push(),this.inSection=!0}endSection(){if(!this.inSection)throw new Error("Not in section");this.inFunction&&this.endFunction(!0),this._pop(!0),this.inSection=!1}_assignLocalIndices(e,t,n,r){e[127]=0,e[126]=0,e[125]=0,e[124]=0,e[123]=0;for(const n in t){const o=t[n];e[o]<=0&&r++,e[o]++}const o=e[127],s=o+e[126],a=s+e[125],i=a+e[124];e[127]=0,e[126]=0,e[125]=0,e[124]=0,e[123]=0;for(const r in t){const c=t[r];let l,p=0;switch(c){case 127:l=0;break;case 126:l=o;break;case 125:l=s;break;case 124:l=a;break;case 123:l=i;break;default:throw new Error(`Unimplemented valtype: ${c}`)}p=e[c]+++l+n,this.locals.set(r,p)}return r}beginFunction(e,t){if(this.inFunction)throw new Error("Already in function");this._push();const n=this.functionTypes[e];this.locals.clear(),this.branchTargets.clear();let r={};const o=[127,126,125,124,123];let s=0;const a=this._assignParameterIndices(n[1]);t?s=this._assignLocalIndices(r,t,a,s):r={},this.appendULeb(s);for(let e=0;e<o.length;e++){const t=o[e],n=r[t];n&&(this.appendULeb(n),this.appendU8(t))}this.inFunction=!0}endFunction(e){if(!this.inFunction)throw new Error("Not in function");if(this.activeBlocks>0)throw new Error(`${this.activeBlocks} unclosed block(s) at end of function`);const t=this._pop(e);return this.inFunction=!1,t}block(e,t){const n=this.appendU8(t||2);return e?this.appendU8(e):this.appendU8(64),this.activeBlocks++,n}endBlock(){if(this.activeBlocks<=0)throw new Error("No blocks active");this.activeBlocks--,this.appendU8(11)}arg(e,t){const n="string"==typeof e?this.locals.has(e)?this.locals.get(e):void 0:e;if("number"!=typeof n)throw new Error("No local named "+e);t&&this.appendU8(t),this.appendULeb(n)}local(e,t){const n="string"==typeof e?this.locals.has(e)?this.locals.get(e):void 0:e+this.argumentCount;if("number"!=typeof n)throw new Error("No local named "+e);t?this.appendU8(t):this.appendU8(32),this.appendULeb(n)}appendMemarg(e,t){this.appendULeb(t),this.appendULeb(e)}lea(e,t){"string"==typeof e?this.local(e):this.i32_const(e),this.i32_const(t),this.appendU8(106)}getArrayView(e){if(this.stackSize>1)throw new Error("Jiterpreter block stack not empty");return this.stack[0].getArrayView(e)}getConstants(){const e={};for(let t=0;t<this.constantSlots.length;t++)e[t.toString(36)]=this.constantSlots[t];return e}}class Cs{constructor(){this.textBuf=new Uint8Array(1024),this.capacity=16384,this.buffer=Xe._malloc(this.capacity),Y().fill(0,this.buffer,this.buffer+this.capacity),this.size=0,this.clear(),"function"==typeof TextEncoder&&(this.encoder=new TextEncoder)}clear(){this.size=0}appendU8(e){if(this.size>=this.capacity)throw new Error("Buffer full");const t=this.size;return Y()[this.buffer+this.size++]=e,t}appendU32(e){const t=this.size;return o.mono_jiterp_write_number_unaligned(this.buffer+this.size,e,0),this.size+=4,t}appendI32(e){const t=this.size;return o.mono_jiterp_write_number_unaligned(this.buffer+this.size,e,1),this.size+=4,t}appendF32(e){const t=this.size;return o.mono_jiterp_write_number_unaligned(this.buffer+this.size,e,2),this.size+=4,t}appendF64(e){const t=this.size;return o.mono_jiterp_write_number_unaligned(this.buffer+this.size,e,3),this.size+=8,t}appendBoundaryValue(e,t){if(this.size+8>=this.capacity)throw new Error("Buffer full");const n=o.mono_jiterp_encode_leb_signed_boundary(this.buffer+this.size,e,t);if(n<1)throw new Error(`Failed to encode ${e} bit boundary value with sign ${t}`);return this.size+=n,n}appendULeb(e){if("number"!=typeof e&&ut(!1,`appendULeb expected number but got ${e}`),e>=0||ut(!1,"cannot pass negative value to appendULeb"),e<127){if(this.size+1>=this.capacity)throw new Error("Buffer full");return this.appendU8(e),1}if(this.size+8>=this.capacity)throw new Error("Buffer full");const t=o.mono_jiterp_encode_leb52(this.buffer+this.size,e,0);if(t<1)throw new Error(`Failed to encode value '${e}' as unsigned leb`);return this.size+=t,t}appendLeb(e){if("number"!=typeof e&&ut(!1,`appendLeb expected number but got ${e}`),this.size+8>=this.capacity)throw new Error("Buffer full");const t=o.mono_jiterp_encode_leb52(this.buffer+this.size,e,1);if(t<1)throw new Error(`Failed to encode value '${e}' as signed leb`);return this.size+=t,t}appendLebRef(e,t){if(this.size+8>=this.capacity)throw new Error("Buffer full");const n=o.mono_jiterp_encode_leb64_ref(this.buffer+this.size,e,t?1:0);if(n<1)throw new Error("Failed to encode value as leb");return this.size+=n,n}copyTo(e,t){"number"!=typeof t&&(t=this.size),Y().copyWithin(e.buffer+e.size,this.buffer,this.buffer+t),e.size+=t}appendBytes(e,t){const n=this.size,r=Y();return e.buffer===r.buffer?("number"!=typeof t&&(t=e.length),r.copyWithin(this.buffer+n,e.byteOffset,e.byteOffset+t),this.size+=t):("number"==typeof t&&(e=new Uint8Array(e.buffer,e.byteOffset,t)),this.getArrayView(!0).set(e,this.size),this.size+=e.length),n}appendName(e){let t=e.length,n=1===e.length?e.charCodeAt(0):-1;if(n>127&&(n=-1),t&&n<0)if(this.encoder)t=this.encoder.encodeInto(e,this.textBuf).written||0;else for(let n=0;n<t;n++){const t=e.charCodeAt(n);if(t>127)throw new Error("Out of range character and no TextEncoder available");this.textBuf[n]=t}this.appendULeb(t),n>=0?this.appendU8(n):t>1&&this.appendBytes(this.textBuf,t)}getArrayView(e){return new Uint8Array(Y().buffer,this.buffer,e?this.capacity:this.size)}}class Os{constructor(e){this.segments=[],this.backBranchTargets=null,this.lastSegmentEnd=0,this.overheadBytes=0,this.blockStack=[],this.backDispatchOffsets=[],this.dispatchTable=new Map,this.observedBackBranchTargets=new Set,this.trace=0,this.builder=e}initialize(e,t,n){this.segments.length=0,this.blockStack.length=0,this.startOfBody=e,this.backBranchTargets=t,this.base=this.builder.base,this.ip=this.lastSegmentStartIp=this.firstOpcodeIp=this.builder.base,this.lastSegmentEnd=0,this.overheadBytes=10,this.dispatchTable.clear(),this.observedBackBranchTargets.clear(),this.trace=n,this.backDispatchOffsets.length=0}entry(e){this.entryIp=e;const t=o.mono_jiterp_get_opcode_info(674,1);return this.firstOpcodeIp=e+2*t,this.appendBlob(),1!==this.segments.length&&ut(!1,"expected 1 segment"),"blob"!==this.segments[0].type&&ut(!1,"expected blob"),this.entryBlob=this.segments[0],this.segments.length=0,this.overheadBytes+=9,this.backBranchTargets&&(this.overheadBytes+=20,this.overheadBytes+=this.backBranchTargets.length),this.firstOpcodeIp}appendBlob(){this.builder.current.size!==this.lastSegmentEnd&&(this.segments.push({type:"blob",ip:this.lastSegmentStartIp,start:this.lastSegmentEnd,length:this.builder.current.size-this.lastSegmentEnd}),this.lastSegmentStartIp=this.ip,this.lastSegmentEnd=this.builder.current.size,this.overheadBytes+=2)}startBranchBlock(e,t){this.appendBlob(),this.segments.push({type:"branch-block-header",ip:e,isBackBranchTarget:t}),this.overheadBytes+=1}branch(e,t,n){t&&this.observedBackBranchTargets.add(e),this.appendBlob(),this.segments.push({type:"branch",from:this.ip,target:e,isBackward:t,branchType:n}),this.overheadBytes+=4,t&&(this.overheadBytes+=4)}emitBlob(e,t){const n=t.subarray(e.start,e.start+e.length);this.builder.appendBytes(n)}generate(){this.appendBlob();const e=this.builder.endFunction(!1);this.builder._push(),this.builder.base=this.base,this.emitBlob(this.entryBlob,e),this.backBranchTargets&&this.builder.block(64,3);for(let e=0;e<this.segments.length;e++){const t=this.segments[e];"branch-block-header"===t.type&&this.blockStack.push(t.ip)}this.blockStack.sort(((e,t)=>e-t));for(let e=0;e<this.blockStack.length;e++)this.builder.block(64);if(this.backBranchTargets){this.backDispatchOffsets.length=0;for(let e=0;e<this.backBranchTargets.length;e++){const t=2*this.backBranchTargets[e]+this.startOfBody;this.blockStack.indexOf(t)<0||this.observedBackBranchTargets.has(t)&&(this.dispatchTable.set(t,this.backDispatchOffsets.length+1),this.backDispatchOffsets.push(t))}if(0===this.backDispatchOffsets.length)this.trace>0&&Fe("No back branch targets were reachable after filtering");else if(1===this.backDispatchOffsets.length)this.trace>0&&(this.backDispatchOffsets[0]===this.entryIp?Fe(`Exactly one back dispatch offset and it was the entry point 0x${this.entryIp.toString(16)}`):Fe(`Exactly one back dispatch offset and it was 0x${this.backDispatchOffsets[0].toString(16)}`)),this.builder.local("disp"),this.builder.appendU8(13),this.builder.appendULeb(this.blockStack.indexOf(this.backDispatchOffsets[0]));else{this.trace>0&&Fe(`${this.backDispatchOffsets.length} back branch offsets after filtering.`),this.builder.block(64),this.builder.block(64),this.builder.local("disp"),this.builder.appendU8(14),this.builder.appendULeb(this.backDispatchOffsets.length+1),this.builder.appendULeb(1);for(let e=0;e<this.backDispatchOffsets.length;e++)this.builder.appendULeb(this.blockStack.indexOf(this.backDispatchOffsets[e])+2);this.builder.appendULeb(0),this.builder.endBlock(),this.builder.appendU8(0),this.builder.endBlock()}this.backDispatchOffsets.length>0&&this.blockStack.push(0)}this.trace>1&&Fe(`blockStack=${this.blockStack}`);for(let t=0;t<this.segments.length;t++){const n=this.segments[t];switch(n.type){case"blob":this.emitBlob(n,e);break;case"branch-block-header":{const e=this.blockStack.indexOf(n.ip);0!==e&&ut(!1,`expected ${n.ip} on top of blockStack but found it at index ${e}, top is ${this.blockStack[0]}`),this.builder.endBlock(),this.blockStack.shift();break}case"branch":{const e=n.isBackward?0:n.target;let t,r=this.blockStack.indexOf(e),o=!1;if(n.isBackward&&(this.dispatchTable.has(n.target)?(t=this.dispatchTable.get(n.target),this.trace>1&&Fe(`backward br from ${n.from.toString(16)} to ${n.target.toString(16)}: disp=${t}`),o=!0):(this.trace>0&&Fe(`br from ${n.from.toString(16)} to ${n.target.toString(16)} failed: back branch target not in dispatch table`),r=-1)),r>=0||o){let e=0;switch(n.branchType){case 2:this.builder,n.from,void 0!==t&&(this.builder.i32_const(t),this.builder.local("disp",33)),this.builder.appendU8(12);break;case 3:this.builder.block(64,4),this.builder,n.from,void 0!==t&&(this.builder.i32_const(t),this.builder.local("disp",33)),this.builder.appendU8(12),e=1;break;case 0:void 0!==t&&(this.builder.i32_const(t),this.builder.local("disp",33)),this.builder.appendU8(12);break;case 1:void 0!==t?(this.builder.block(64,4),this.builder.i32_const(t),this.builder.local("disp",33),e=1,this.builder.appendU8(12)):this.builder.appendU8(13);break;default:throw new Error("Unimplemented branch type")}this.builder.appendULeb(e+r),e&&this.builder.endBlock(),this.trace>1&&Fe(`br from ${n.from.toString(16)} to ${n.target.toString(16)} breaking out ${e+r+1} level(s)`)}else{if(this.trace>0){const e=this.base;n.target>=e&&n.target<this.exitIp?Fe(`br from ${n.from.toString(16)} to ${n.target.toString(16)} failed (inside of trace!)`):this.trace>1&&Fe(`br from ${n.from.toString(16)} to ${n.target.toString(16)} failed (outside of trace 0x${e.toString(16)} - 0x${this.exitIp.toString(16)})`)}const e=1===n.branchType||3===n.branchType;e&&this.builder.block(64,4),Ps(this.builder,n.target,4),e&&this.builder.endBlock()}break}default:throw new Error("unreachable")}}return this.backBranchTargets&&(this.blockStack.length<=1||ut(!1,"expected one or zero entries in the block stack at the end"),this.blockStack.length&&this.blockStack.shift(),this.builder.endBlock()),0!==this.blockStack.length&&ut(!1,`expected block stack to be empty at end of function but it was ${this.blockStack}`),this.builder.ip_const(this.exitIp),this.builder.appendU8(15),this.builder.appendU8(11),this.builder._pop(!1)}}let Ds;const Fs={},Ms=globalThis.performance&&globalThis.performance.now?globalThis.performance.now.bind(globalThis.performance):Date.now;function Ps(e,t,n){e.ip_const(t),e.options.countBailouts&&(e.i32_const(e.traceIndex),e.i32_const(n),e.callImport("bailout")),e.appendU8(15)}function Vs(e,t,n,r){e.local("cinfo"),e.block(64,4),e.local("cinfo"),e.local("disp"),e.appendU8(54),e.appendMemarg(Ys(19),0),n<=e.options.monitoringLongDistance+2&&(e.local("cinfo"),e.i32_const(n),e.appendU8(54),e.appendMemarg(Ys(20),0)),e.endBlock(),e.ip_const(t),e.options.countBailouts&&(e.i32_const(e.traceIndex),e.i32_const(r),e.callImport("bailout")),e.appendU8(15)}function zs(){if(Ds||(Ds=ot.getWasmIndirectFunctionTable()),!Ds)throw new Error("Module did not export the indirect function table");return Ds}function Hs(e,t){t||ut(!1,"Attempting to set null function into table");const n=o.mono_jiterp_allocate_table_entry(e);return n>0&&zs().set(n,t),n}function Ws(e,t,n,r,o){if(r<=0)return o&&e.appendU8(26),!0;if(r>=Ls)return!1;const s=o?"memop_dest":"pLocals";o&&e.local(s,33);let a=o?0:t;if(e.options.enableSimd){const t=16;for(;r>=t;)e.local(s),e.v128_const(0),e.appendSimd(11),e.appendMemarg(a,0),a+=t,r-=t}for(;r>=8;)e.local(s),e.i52_const(0),e.appendU8(55),e.appendMemarg(a,0),a+=8,r-=8;for(;r>=1;){e.local(s),e.i32_const(0);let t=r%4;switch(t){case 0:t=4,e.appendU8(54);break;case 1:e.appendU8(58);break;case 3:case 2:t=2,e.appendU8(59)}e.appendMemarg(a,0),a+=t,r-=t}return!0}function qs(e,t,n){Ws(e,0,0,n,!0)||(e.i32_const(t),e.i32_const(n),e.appendU8(252),e.appendU8(11),e.appendU8(0))}function Gs(e,t,n,r,o,s,a){if(r<=0)return o&&(e.appendU8(26),e.appendU8(26)),!0;if(r>=Rs)return!1;o?(s=s||"memop_dest",a=a||"memop_src",e.local(a,33),e.local(s,33)):s&&a||(s=a="pLocals");let i=o?0:t,c=o?0:n;if(e.options.enableSimd){const t=16;for(;r>=t;)e.local(s),e.local(a),e.appendSimd(0,!0),e.appendMemarg(c,0),e.appendSimd(11),e.appendMemarg(i,0),i+=t,c+=t,r-=t}for(;r>=8;)e.local(s),e.local(a),e.appendU8(41),e.appendMemarg(c,0),e.appendU8(55),e.appendMemarg(i,0),i+=8,c+=8,r-=8;for(;r>=1;){let t,n,o=r%4;switch(o){case 0:o=4,t=40,n=54;break;default:case 1:o=1,t=44,n=58;break;case 3:case 2:o=2,t=46,n=59}e.local(s),e.local(a),e.appendU8(t),e.appendMemarg(c,0),e.appendU8(n),e.appendMemarg(i,0),c+=o,i+=o,r-=o}return!0}function Js(e,t){return Gs(e,0,0,t,!0)||(e.i32_const(t),e.appendU8(252),e.appendU8(10),e.appendU8(0),e.appendU8(0)),!0}function Xs(){const e=la(5,1);e>=$s&&(Fe(`Disabling jiterpreter after ${e} failures`),ia({enableTraces:!1,enableInterpEntry:!1,enableJitCall:!1}))}const Qs={};function Ys(e){const t=Qs[e];return void 0===t?Qs[e]=o.mono_jiterp_get_member_offset(e):t}function Zs(e){const t=Xe.wasmExports[e];if("function"!=typeof t)throw new Error(`raw cwrap ${e} not found`);return t}const Ks={};function ea(e){let t=Ks[e];return"number"!=typeof t&&(t=Ks[e]=o.mono_jiterp_get_opcode_value_table_entry(e)),t}function ta(e,t){return[e,e,t]}let na;function ra(){if(!o.mono_wasm_is_zero_page_reserved())return!1;if(!0===na)return!1;const e=K();for(let t=0;t<8;t++)if(0!==e[t])return!1===na&&Pe(`Zero page optimizations are enabled but garbage appeared in memory at address ${4*t}: ${e[t]}`),na=!0,!1;return na=!1,!0}const oa={enableTraces:"jiterpreter-traces-enabled",enableInterpEntry:"jiterpreter-interp-entry-enabled",enableJitCall:"jiterpreter-jit-call-enabled",enableBackwardBranches:"jiterpreter-backward-branch-entries-enabled",enableCallResume:"jiterpreter-call-resume-enabled",enableWasmEh:"jiterpreter-wasm-eh-enabled",enableSimd:"jiterpreter-simd-enabled",enableAtomics:"jiterpreter-atomics-enabled",zeroPageOptimization:"jiterpreter-zero-page-optimization",cprop:"jiterpreter-constant-propagation",enableStats:"jiterpreter-stats-enabled",disableHeuristic:"jiterpreter-disable-heuristic",estimateHeat:"jiterpreter-estimate-heat",countBailouts:"jiterpreter-count-bailouts",dumpTraces:"jiterpreter-dump-traces",useConstants:"jiterpreter-use-constants",eliminateNullChecks:"jiterpreter-eliminate-null-checks",noExitBackwardBranches:"jiterpreter-backward-branches-enabled",directJitCalls:"jiterpreter-direct-jit-calls",minimumTraceValue:"jiterpreter-minimum-trace-value",minimumTraceHitCount:"jiterpreter-minimum-trace-hit-count",monitoringPeriod:"jiterpreter-trace-monitoring-period",monitoringShortDistance:"jiterpreter-trace-monitoring-short-distance",monitoringLongDistance:"jiterpreter-trace-monitoring-long-distance",monitoringMaxAveragePenalty:"jiterpreter-trace-monitoring-max-average-penalty",backBranchBoost:"jiterpreter-back-branch-boost",jitCallHitCount:"jiterpreter-jit-call-hit-count",jitCallFlushThreshold:"jiterpreter-jit-call-queue-flush-threshold",interpEntryHitCount:"jiterpreter-interp-entry-hit-count",interpEntryFlushThreshold:"jiterpreter-interp-entry-queue-flush-threshold",wasmBytesLimit:"jiterpreter-wasm-bytes-limit",tableSize:"jiterpreter-table-size",aotTableSize:"jiterpreter-aot-table-size"};let sa=-1,aa={};function ia(e){for(const t in e){const n=oa[t];if(!n){Pe(`Unrecognized jiterpreter option: ${t}`);continue}const r=e[t];"boolean"==typeof r?o.mono_jiterp_parse_option((r?"--":"--no-")+n):"number"==typeof r?o.mono_jiterp_parse_option(`--${n}=${r}`):Pe(`Jiterpreter option must be a boolean or a number but was ${typeof r} '${r}'`)}}function ca(e){return o.mono_jiterp_get_counter(e)}function la(e,t){return o.mono_jiterp_modify_counter(e,t)}function pa(){const e=o.mono_jiterp_get_options_version();return e!==sa&&(function(){aa={};for(const e in oa){const t=o.mono_jiterp_get_option_as_int(oa[e]);t>-2147483647?aa[e]=t:Fe(`Failed to retrieve value of option ${oa[e]}`)}}(),sa=e),aa}function ua(e,t,n,r){const s=zs(),a=t,i=a+n-1;return i<s.length||ut(!1,`Last index out of range: ${i} >= ${s.length}`),s.set(a,r),o.mono_jiterp_initialize_table(e,a,i),t+n}let da=!1;const fa=["Unknown","InterpreterTiering","NullCheck","VtableNotInitialized","Branch","BackwardBranch","ConditionalBranch","ConditionalBackwardBranch","ComplexBranch","ArrayLoadFailed","ArrayStoreFailed","StringOperationFailed","DivideByZero","Overflow","Return","Call","Throw","AllocFailed","SpanOperationFailed","CastFailed","SafepointBranchTaken","UnboxFailed","CallDelegate","Debugging","Icall","UnexpectedRetIp","LeaveCheck"],_a={2:["V128_I1_NEGATION","V128_I2_NEGATION","V128_I4_NEGATION","V128_ONES_COMPLEMENT","V128_U2_WIDEN_LOWER","V128_U2_WIDEN_UPPER","V128_I1_CREATE_SCALAR","V128_I2_CREATE_SCALAR","V128_I4_CREATE_SCALAR","V128_I8_CREATE_SCALAR","V128_I1_EXTRACT_MSB","V128_I2_EXTRACT_MSB","V128_I4_EXTRACT_MSB","V128_I8_EXTRACT_MSB","V128_I1_CREATE","V128_I2_CREATE","V128_I4_CREATE","V128_I8_CREATE","SplatX1","SplatX2","SplatX4","SplatX8","NegateD1","NegateD2","NegateD4","NegateD8","NegateR4","NegateR8","SqrtR4","SqrtR8","CeilingR4","CeilingR8","FloorR4","FloorR8","TruncateR4","TruncateR8","RoundToNearestR4","RoundToNearestR8","NotANY","AnyTrueANY","AllTrueD1","AllTrueD2","AllTrueD4","AllTrueD8","PopCountU1","BitmaskD1","BitmaskD2","BitmaskD4","BitmaskD8","AddPairwiseWideningI1","AddPairwiseWideningU1","AddPairwiseWideningI2","AddPairwiseWideningU2","AbsI1","AbsI2","AbsI4","AbsI8","AbsR4","AbsR8","ConvertToSingleI4","ConvertToSingleU4","ConvertToSingleR8","ConvertToDoubleLowerI4","ConvertToDoubleLowerU4","ConvertToDoubleLowerR8","ConvertToInt32SaturateR4","ConvertToUInt32SaturateR4","ConvertToInt32SaturateR8","ConvertToUInt32SaturateR8","SignExtendWideningLowerD1","SignExtendWideningLowerD2","SignExtendWideningLowerD4","SignExtendWideningUpperD1","SignExtendWideningUpperD2","SignExtendWideningUpperD4","ZeroExtendWideningLowerD1","ZeroExtendWideningLowerD2","ZeroExtendWideningLowerD4","ZeroExtendWideningUpperD1","ZeroExtendWideningUpperD2","ZeroExtendWideningUpperD4","LoadVector128ANY","LoadScalarVector128X4","LoadScalarVector128X8","LoadScalarAndSplatVector128X1","LoadScalarAndSplatVector128X2","LoadScalarAndSplatVector128X4","LoadScalarAndSplatVector128X8","LoadWideningVector128I1","LoadWideningVector128U1","LoadWideningVector128I2","LoadWideningVector128U2","LoadWideningVector128I4","LoadWideningVector128U4"],3:["V128_I1_ADD","V128_I2_ADD","V128_I4_ADD","V128_R4_ADD","V128_I1_SUB","V128_I2_SUB","V128_I4_SUB","V128_R4_SUB","V128_BITWISE_AND","V128_BITWISE_OR","V128_BITWISE_EQUALITY","V128_BITWISE_INEQUALITY","V128_R4_FLOAT_EQUALITY","V128_R8_FLOAT_EQUALITY","V128_EXCLUSIVE_OR","V128_I1_MULTIPLY","V128_I2_MULTIPLY","V128_I4_MULTIPLY","V128_R4_MULTIPLY","V128_R4_DIVISION","V128_I1_LEFT_SHIFT","V128_I2_LEFT_SHIFT","V128_I4_LEFT_SHIFT","V128_I8_LEFT_SHIFT","V128_I1_RIGHT_SHIFT","V128_I2_RIGHT_SHIFT","V128_I4_RIGHT_SHIFT","V128_I1_URIGHT_SHIFT","V128_I2_URIGHT_SHIFT","V128_I4_URIGHT_SHIFT","V128_I8_URIGHT_SHIFT","V128_U1_NARROW","V128_U1_GREATER_THAN","V128_I1_LESS_THAN","V128_U1_LESS_THAN","V128_I2_LESS_THAN","V128_I1_EQUALS","V128_I2_EQUALS","V128_I4_EQUALS","V128_R4_EQUALS","V128_I8_EQUALS","V128_I1_EQUALS_ANY","V128_I2_EQUALS_ANY","V128_I4_EQUALS_ANY","V128_I8_EQUALS_ANY","V128_AND_NOT","V128_U2_LESS_THAN_EQUAL","V128_I1_SHUFFLE","V128_I2_SHUFFLE","V128_I4_SHUFFLE","V128_I8_SHUFFLE","ExtractScalarI1","ExtractScalarU1","ExtractScalarI2","ExtractScalarU2","ExtractScalarD4","ExtractScalarD8","ExtractScalarR4","ExtractScalarR8","SwizzleD1","AddD1","AddD2","AddD4","AddD8","AddR4","AddR8","SubtractD1","SubtractD2","SubtractD4","SubtractD8","SubtractR4","SubtractR8","MultiplyD2","MultiplyD4","MultiplyD8","MultiplyR4","MultiplyR8","DivideR4","DivideR8","DotI2","ShiftLeftD1","ShiftLeftD2","ShiftLeftD4","ShiftLeftD8","ShiftRightArithmeticD1","ShiftRightArithmeticD2","ShiftRightArithmeticD4","ShiftRightArithmeticD8","ShiftRightLogicalD1","ShiftRightLogicalD2","ShiftRightLogicalD4","ShiftRightLogicalD8","AndANY","AndNotANY","OrANY","XorANY","CompareEqualD1","CompareEqualD2","CompareEqualD4","CompareEqualD8","CompareEqualR4","CompareEqualR8","CompareNotEqualD1","CompareNotEqualD2","CompareNotEqualD4","CompareNotEqualD8","CompareNotEqualR4","CompareNotEqualR8","CompareLessThanI1","CompareLessThanU1","CompareLessThanI2","CompareLessThanU2","CompareLessThanI4","CompareLessThanU4","CompareLessThanI8","CompareLessThanR4","CompareLessThanR8","CompareLessThanOrEqualI1","CompareLessThanOrEqualU1","CompareLessThanOrEqualI2","CompareLessThanOrEqualU2","CompareLessThanOrEqualI4","CompareLessThanOrEqualU4","CompareLessThanOrEqualI8","CompareLessThanOrEqualR4","CompareLessThanOrEqualR8","CompareGreaterThanI1","CompareGreaterThanU1","CompareGreaterThanI2","CompareGreaterThanU2","CompareGreaterThanI4","CompareGreaterThanU4","CompareGreaterThanI8","CompareGreaterThanR4","CompareGreaterThanR8","CompareGreaterThanOrEqualI1","CompareGreaterThanOrEqualU1","CompareGreaterThanOrEqualI2","CompareGreaterThanOrEqualU2","CompareGreaterThanOrEqualI4","CompareGreaterThanOrEqualU4","CompareGreaterThanOrEqualI8","CompareGreaterThanOrEqualR4","CompareGreaterThanOrEqualR8","ConvertNarrowingSaturateSignedI2","ConvertNarrowingSaturateSignedI4","ConvertNarrowingSaturateUnsignedI2","ConvertNarrowingSaturateUnsignedI4","MultiplyWideningLowerI1","MultiplyWideningLowerI2","MultiplyWideningLowerI4","MultiplyWideningLowerU1","MultiplyWideningLowerU2","MultiplyWideningLowerU4","MultiplyWideningUpperI1","MultiplyWideningUpperI2","MultiplyWideningUpperI4","MultiplyWideningUpperU1","MultiplyWideningUpperU2","MultiplyWideningUpperU4","AddSaturateI1","AddSaturateU1","AddSaturateI2","AddSaturateU2","SubtractSaturateI1","SubtractSaturateU1","SubtractSaturateI2","SubtractSaturateU2","MultiplyRoundedSaturateQ15I2","MinI1","MinI2","MinI4","MinU1","MinU2","MinU4","MaxI1","MaxI2","MaxI4","MaxU1","MaxU2","MaxU4","AverageRoundedU1","AverageRoundedU2","MinR4","MinR8","MaxR4","MaxR8","PseudoMinR4","PseudoMinR8","PseudoMaxR4","PseudoMaxR8","StoreANY"],4:["V128_CONDITIONAL_SELECT","ReplaceScalarD1","ReplaceScalarD2","ReplaceScalarD4","ReplaceScalarD8","ReplaceScalarR4","ReplaceScalarR8","ShuffleD1","BitwiseSelectANY","LoadScalarAndInsertX1","LoadScalarAndInsertX2","LoadScalarAndInsertX4","LoadScalarAndInsertX8","StoreSelectedScalarX1","StoreSelectedScalarX2","StoreSelectedScalarX4","StoreSelectedScalarX8"]},ma={13:[65,0],14:[65,1]},ha={456:168,462:174,457:170,463:176},ga={508:[69,40,54],428:[106,40,54],430:[107,40,54],432:[107,40,54],436:[115,40,54],429:[124,41,55],431:[125,41,55],433:[125,41,55],437:[133,41,55],511:[106,40,54],515:[108,40,54],513:[124,41,55],517:[126,41,55],434:[140,42,56],435:[154,43,57],464:[178,40,56],467:[183,40,57],438:[184,40,57],465:[180,41,56],468:[185,41,57],439:[186,41,57],469:[187,42,57],466:[182,43,56],460:[1,52,55],461:[1,53,55],444:[113,40,54],452:[113,40,54],440:[117,40,54],448:[117,40,54],445:[113,41,54],453:[113,41,54],441:[117,41,54],449:[117,41,54],525:[116,40,54],526:[134,41,55],527:[117,40,54],528:[135,41,55],523:[118,40,54],524:[136,41,55],638:[119,40,54],639:[137,41,55],640:[120,40,54],641:[138,41,55],642:[103,40,54],644:[104,40,54],646:[105,40,54],643:[121,41,55],645:[122,41,55],647:[123,41,55],512:[106,40,54],516:[108,40,54],514:[124,41,55],518:[126,41,55],519:[113,40,54],520:[113,40,54],521:[114,40,54],522:[114,40,54]},ba={394:187,395:1,398:187,399:1,402:187,403:1,406:187,407:1,412:187,413:1,416:187,417:1,426:187,427:1,420:187,421:1,65536:187,65537:187,65535:187,65539:1,65540:1,65538:1},ya={344:[106,40,54],362:[106,40,54],364:[106,40,54],348:[107,40,54],352:[108,40,54],366:[108,40,54],368:[108,40,54],356:[109,40,54],360:[110,40,54],380:[111,40,54],384:[112,40,54],374:[113,40,54],376:[114,40,54],378:[115,40,54],388:[116,40,54],390:[117,40,54],386:[118,40,54],345:[124,41,55],349:[125,41,55],353:[126,41,55],357:[127,41,55],381:[129,41,55],361:[128,41,55],385:[130,41,55],375:[131,41,55],377:[132,41,55],379:[133,41,55],389:[134,41,55],391:[135,41,55],387:[136,41,55],346:[146,42,56],350:[147,42,56],354:[148,42,56],358:[149,42,56],347:[160,43,57],351:[161,43,57],355:[162,43,57],359:[163,43,57],392:[70,40,54],396:[71,40,54],414:[72,40,54],400:[74,40,54],418:[76,40,54],404:[78,40,54],424:[73,40,54],410:[75,40,54],422:[77,40,54],408:[79,40,54],393:[81,41,54],397:[82,41,54],415:[83,41,54],401:[85,41,54],419:[87,41,54],405:[89,41,54],425:[84,41,54],411:[86,41,54],423:[88,41,54],409:[90,41,54]},wa={187:392,207:396,195:400,215:410,199:414,223:424,191:404,211:408,203:418,219:422,231:[392,!1,!0],241:[396,!1,!0],235:[400,!1,!0],245:[410,!1,!0],237:[414,!1,!0],249:[424,!1,!0],233:[404,!1,!0],243:[408,!1,!0],239:[418,!1,!0],247:[422,!1,!0],251:[392,65,!0],261:[396,65,!0],255:[400,65,!0],265:[410,65,!0],257:[414,65,!0],269:[424,65,!0],253:[404,65,!0],263:[408,65,!0],259:[418,65,!0],267:[422,65,!0],188:393,208:397,196:401,216:411,200:415,224:425,192:405,212:409,204:419,220:423,252:[393,66,!0],256:[401,66,!0],266:[411,66,!0],258:[415,66,!0],270:[425,66,!0],254:[405,66,!0],264:[409,66,!0],260:[419,66,!0],268:[423,66,!0],189:394,209:65535,197:402,217:412,201:416,225:426,193:406,213:65536,205:420,221:65537,190:395,210:65538,198:403,218:413,202:417,226:427,194:407,214:65539,206:421,222:65540},ka={598:[!0,!1,159],625:[!0,!0,145],585:[!0,!1,155],612:[!0,!0,141],591:[!0,!1,156],618:[!0,!0,142],602:[!0,!1,153],629:[!0,!0,139],580:[!0,!1,"acos"],607:[!0,!0,"acosf"],581:[!0,!1,"acosh"],608:[!0,!0,"acoshf"],586:[!0,!1,"cos"],613:[!0,!0,"cosf"],578:[!0,!1,"asin"],605:[!0,!0,"asinf"],579:[!0,!1,"asinh"],606:[!0,!0,"asinhf"],597:[!0,!1,"sin"],624:[!0,!0,"sinf"],582:[!0,!1,"atan"],609:[!0,!0,"atanf"],583:[!0,!1,"atanh"],610:[!0,!0,"atanhf"],600:[!0,!1,"tan"],627:[!0,!0,"tanf"],587:[!0,!1,"cbrt"],614:[!0,!0,"cbrtf"],589:[!0,!1,"exp"],616:[!0,!0,"expf"],592:[!0,!1,"log"],619:[!0,!0,"logf"],593:[!0,!1,"log2"],620:[!0,!0,"log2f"],594:[!0,!1,"log10"],621:[!0,!0,"log10f"],603:[!1,!1,164],630:[!1,!0,150],604:[!1,!1,165],631:[!1,!0,151],584:[!1,!1,"atan2"],611:[!1,!0,"atan2f"],595:[!1,!1,"pow"],622:[!1,!0,"powf"],383:[!1,!1,"fmod"],382:[!1,!0,"fmodf"]},Sa={560:[67,0,0],561:[67,192,0],562:[68,0,1],563:[68,193,1],564:[65,0,2],565:[66,0,3]},va={566:[74,0,0],567:[74,192,0],568:[75,0,1],569:[75,193,1],570:[72,0,2],571:[73,0,3]},Ua={651:1,652:2,653:4,654:8},Ea={651:44,652:46,653:40,654:41},Ta={651:58,652:59,653:54,654:55},xa=new Set([20,21,22,23,24,25,26,27,28,29,30]),Ia={51:[16,54],52:[16,54],53:[8,54],54:[8,54],55:[4,54],57:[4,56],56:[2,55],58:[2,57]},Aa={1:[16,40],2:[8,40],3:[4,40],5:[4,42],4:[2,41],6:[2,43]},ja=new Set([81,84,85,86,87,82,83,88,89,90,91,92,93]),$a={13:[16],14:[8],15:[4],16:[2]},La={10:100,11:132,12:164,13:196},Ra={6:[44,23],7:[46,26],8:[40,28],9:[41,30]};function Ba(e,t){return B(e+2*t)}function Na(e,t){return M(e+2*t)}function Ca(e,t){return O(e+2*t)}function Oa(e){return D(e+Ys(4))}function Da(e,t){const n=D(Oa(e)+Ys(5));return D(n+t*fc)}function Fa(e,t){const n=D(Oa(e)+Ys(12));return D(n+t*fc)}function Ma(e,t,n){if(!n)return!1;for(let r=0;r<n.length;r++)if(2*n[r]+t===e)return!0;return!1}const Pa=new Map;function Va(e,t){if(!oi(e,t))return Pa.get(t)}function za(e,t){const n=Va(e,t);if(void 0!==n)switch(n.type){case"i32":case"v128":return n.value}}const Ha=new Map;let Wa,qa=-1;function Ga(){qa=-1,Ha.clear(),Pa.clear()}function Ja(e){qa===e&&(qa=-1),Ha.delete(e),Pa.delete(e)}function Xa(e,t){for(let n=0;n<t;n+=1)Ja(e+n)}function Qa(e,t,n){e.cfg.startBranchBlock(t,n)}function Ya(e,t,n){let r=0;switch(e%16==0?r=4:e%8==0?r=3:e%4==0?r=2:e%2==0&&(r=1),t){case 253:r=0===n||11===n?Math.min(r,4):0;break;case 41:case 43:case 55:case 57:r=Math.min(r,3);break;case 52:case 53:case 62:case 40:case 42:case 54:case 56:r=Math.min(r,2);break;case 50:case 51:case 46:case 47:case 61:case 59:r=Math.min(r,1);break;default:r=0}return r}function Za(e,t,n,r,o){if(e.options.cprop&&40===n){const n=Va(e,t);if(n)switch(n.type){case"i32":return!(o&&0===n.value||(r||e.i32_const(n.value),0));case"ldloca":return r||ti(e,n.offset,0),!0}}return!1}function Ka(e,t,n,r){if(Za(e,t,n,!1))return;if(e.local("pLocals"),n>=40||ut(!1,`Expected load opcode but got ${n}`),e.appendU8(n),void 0!==r)e.appendULeb(r);else if(253===n)throw new Error("PREFIX_simd ldloc without a simdOpcode");const o=Ya(t,n,r);e.appendMemarg(t,o)}function ei(e,t,n,r){n>=54||ut(!1,`Expected store opcode but got ${n}`),e.appendU8(n),void 0!==r&&e.appendULeb(r);const o=Ya(t,n,r);e.appendMemarg(t,o),Ja(t),void 0!==r&&Ja(t+8)}function ti(e,t,n){"number"!=typeof n&&(n=512),n>0&&Xa(t,n),e.lea("pLocals",t)}function ni(e,t,n,r){Xa(t,r),Ws(e,t,0,r,!1)||(ti(e,t,r),qs(e,n,r))}function ri(e,t,n,r){if(Xa(t,r),Gs(e,t,n,r,!1))return!0;ti(e,t,r),ti(e,n,0),Js(e,r)}function oi(e,t){return 0!==o.mono_jiterp_is_imethod_var_address_taken(Oa(e.frame),t)}function si(e,t,n,r){if(e.allowNullCheckOptimization&&Ha.has(t)&&!oi(e,t))return la(7,1),void(qa===t?r&&e.local("cknull_ptr"):(Ka(e,t,40),e.local("cknull_ptr",r?34:33),qa=t));Ka(e,t,40),e.local("cknull_ptr",34),e.appendU8(69),e.block(64,4),Ps(e,n,2),e.endBlock(),r&&e.local("cknull_ptr"),e.allowNullCheckOptimization&&!oi(e,t)?(Ha.set(t,n),qa=t):qa=-1}function ai(e,t,n){let r,s=54;const a=ma[n];if(a)e.local("pLocals"),e.appendU8(a[0]),r=a[1],e.appendLeb(r);else switch(n){case 15:e.local("pLocals"),r=Na(t,2),e.i32_const(r);break;case 16:e.local("pLocals"),r=Ca(t,2),e.i32_const(r);break;case 17:e.local("pLocals"),e.i52_const(0),s=55;break;case 19:e.local("pLocals"),e.appendU8(66),e.appendLebRef(t+4,!0),s=55;break;case 18:e.local("pLocals"),e.i52_const(Na(t,2)),s=55;break;case 20:e.local("pLocals"),e.appendU8(67),e.appendF32(function(e,t){return n=e+2*t,o.mono_wasm_get_f32_unaligned(n);var n}(t,2)),s=56;break;case 21:e.local("pLocals"),e.appendU8(68),e.appendF64(function(e,t){return n=e+2*t,o.mono_wasm_get_f64_unaligned(n);var n}(t,2)),s=57;break;default:return!1}e.appendU8(s);const i=Ba(t,1);return e.appendMemarg(i,2),Ja(i),"number"==typeof r?Pa.set(i,{type:"i32",value:r}):Pa.delete(i),!0}function ii(e,t,n){let r=40,o=54;switch(n){case 74:r=44;break;case 75:r=45;break;case 76:r=46;break;case 77:r=47;break;case 78:r=45,o=58;break;case 79:r=47,o=59;break;case 80:break;case 81:r=41,o=55;break;case 82:{const n=Ba(t,3);return ri(e,Ba(t,1),Ba(t,2),n),!0}case 83:return ri(e,Ba(t,1),Ba(t,2),8),ri(e,Ba(t,3),Ba(t,4),8),!0;case 84:return ri(e,Ba(t,1),Ba(t,2),8),ri(e,Ba(t,3),Ba(t,4),8),ri(e,Ba(t,5),Ba(t,6),8),!0;case 85:return ri(e,Ba(t,1),Ba(t,2),8),ri(e,Ba(t,3),Ba(t,4),8),ri(e,Ba(t,5),Ba(t,6),8),ri(e,Ba(t,7),Ba(t,8),8),!0;default:return!1}return e.local("pLocals"),Ka(e,Ba(t,2),r),ei(e,Ba(t,1),o),!0}function ci(e,t,n,r){const o=r>=23&&r<=36||r>=50&&r<=60,s=Ba(n,o?2:1),a=Ba(n,3),i=Ba(n,o?1:2),c=e.allowNullCheckOptimization&&Ha.has(s)&&!oi(e,s);36!==r&&45!==r&&si(e,s,n,!1);let l=54,p=40;switch(r){case 23:p=44;break;case 24:p=45;break;case 25:p=46;break;case 26:p=47;break;case 31:case 41:case 27:break;case 43:case 29:p=42,l=56;break;case 44:case 30:p=43,l=57;break;case 37:case 38:l=58;break;case 39:case 40:l=59;break;case 28:case 42:p=41,l=55;break;case 45:return c||e.block(),e.local("pLocals"),e.i32_const(a),e.i32_const(s),e.i32_const(i),e.callImport("stfld_o"),c?(e.appendU8(26),la(7,1)):(e.appendU8(13),e.appendULeb(0),Ps(e,n,2),e.endBlock()),!0;case 32:{const t=Ba(n,4);return ti(e,i,t),e.local("cknull_ptr"),0!==a&&(e.i32_const(a),e.appendU8(106)),Js(e,t),!0}case 46:{const r=Da(t,Ba(n,4));return e.local("cknull_ptr"),0!==a&&(e.i32_const(a),e.appendU8(106)),ti(e,i,0),e.ptr_const(r),e.callImport("value_copy"),!0}case 47:{const t=Ba(n,4);return e.local("cknull_ptr"),0!==a&&(e.i32_const(a),e.appendU8(106)),ti(e,i,0),Js(e,t),!0}case 36:case 35:return e.local("pLocals"),Ka(e,s,40),0!==a&&(e.i32_const(a),e.appendU8(106)),ei(e,i,l),!0;default:return!1}return o&&e.local("pLocals"),e.local("cknull_ptr"),o?(e.appendU8(p),e.appendMemarg(a,0),ei(e,i,l),!0):(Ka(e,i,p),e.appendU8(l),e.appendMemarg(a,0),!0)}function li(e,t,n,r){const o=r>=23&&r<=36||r>=50&&r<=60,s=Ba(n,1),a=Da(t,Ba(n,2)),i=Da(t,Ba(n,3));!function(e,t,n){e.block(),e.ptr_const(t),e.appendU8(45),e.appendMemarg(Ys(0),0),e.appendU8(13),e.appendULeb(0),Ps(e,n,3),e.endBlock()}(e,a,n);let c=54,l=40;switch(r){case 50:l=44;break;case 51:l=45;break;case 52:l=46;break;case 53:l=47;break;case 58:case 65:case 54:break;case 67:case 56:l=42,c=56;break;case 68:case 57:l=43,c=57;break;case 61:case 62:c=58;break;case 63:case 64:c=59;break;case 55:case 66:l=41,c=55;break;case 69:return e.ptr_const(i),ti(e,s,0),e.callImport("copy_ptr"),!0;case 59:{const t=Ba(n,4);return ti(e,s,t),e.ptr_const(i),Js(e,t),!0}case 72:return e.local("pLocals"),e.ptr_const(i),ei(e,s,c),!0;default:return!1}return o?(e.local("pLocals"),e.ptr_const(i),e.appendU8(l),e.appendMemarg(0,0),ei(e,s,c),!0):(e.ptr_const(i),Ka(e,s,l),e.appendU8(c),e.appendMemarg(0,0),!0)}function pi(e,t,n){let r,o,s,a,i="math_lhs32",c="math_rhs32",l=!1;const p=ba[n];if(p){e.local("pLocals");const r=1==p;return Ka(e,Ba(t,2),r?43:42),r||e.appendU8(p),Ka(e,Ba(t,3),r?43:42),r||e.appendU8(p),e.i32_const(n),e.callImport("relop_fp"),ei(e,Ba(t,1),54),!0}switch(n){case 382:case 383:return hi(e,t,n);default:if(a=ya[n],!a)return!1;a.length>3?(r=a[1],o=a[2],s=a[3]):(r=o=a[1],s=a[2])}switch(n){case 356:case 357:case 360:case 361:case 380:case 381:case 384:case 385:{const s=361===n||385===n||357===n||381===n;i=s?"math_lhs64":"math_lhs32",c=s?"math_rhs64":"math_rhs32",e.block(),Ka(e,Ba(t,2),r),e.local(i,33),Ka(e,Ba(t,3),o),e.local(c,34),l=!0,s&&(e.appendU8(80),e.appendU8(69)),e.appendU8(13),e.appendULeb(0),Ps(e,t,12),e.endBlock(),356!==n&&380!==n&&357!==n&&381!==n||(e.block(),e.local(c),s?e.i52_const(-1):e.i32_const(-1),e.appendU8(s?82:71),e.appendU8(13),e.appendULeb(0),e.local(i),e.appendU8(s?66:65),e.appendBoundaryValue(s?64:32,-1),e.appendU8(s?82:71),e.appendU8(13),e.appendULeb(0),Ps(e,t,13),e.endBlock());break}case 362:case 364:case 366:case 368:Ka(e,Ba(t,2),r),e.local(i,34),Ka(e,Ba(t,3),o),e.local(c,34),e.i32_const(n),e.callImport(364===n||368===n?"ckovr_u4":"ckovr_i4"),e.block(64,4),Ps(e,t,13),e.endBlock(),l=!0}return e.local("pLocals"),l?(e.local(i),e.local(c)):(Ka(e,Ba(t,2),r),Ka(e,Ba(t,3),o)),e.appendU8(a[0]),ei(e,Ba(t,1),s),!0}function ui(e,t,n){const r=ga[n];if(!r)return!1;const o=r[1],s=r[2];switch((n<472||n>507)&&e.local("pLocals"),n){case 428:case 430:Ka(e,Ba(t,2),o),e.i32_const(1);break;case 432:e.i32_const(0),Ka(e,Ba(t,2),o);break;case 436:Ka(e,Ba(t,2),o),e.i32_const(-1);break;case 444:case 445:Ka(e,Ba(t,2),o),41===o&&e.appendU8(167),e.i32_const(255);break;case 452:case 453:Ka(e,Ba(t,2),o),41===o&&e.appendU8(167),e.i32_const(65535);break;case 440:case 441:Ka(e,Ba(t,2),o),41===o&&e.appendU8(167),e.i32_const(24),e.appendU8(116),e.i32_const(24);break;case 448:case 449:Ka(e,Ba(t,2),o),41===o&&e.appendU8(167),e.i32_const(16),e.appendU8(116),e.i32_const(16);break;case 429:case 431:Ka(e,Ba(t,2),o),e.i52_const(1);break;case 433:e.i52_const(0),Ka(e,Ba(t,2),o);break;case 437:Ka(e,Ba(t,2),o),e.i52_const(-1);break;case 511:case 515:case 519:case 521:case 525:case 527:case 523:case 638:case 640:Ka(e,Ba(t,2),o),e.i32_const(Na(t,3));break;case 512:case 516:case 520:case 522:Ka(e,Ba(t,2),o),e.i32_const(Ca(t,3));break;case 513:case 517:case 526:case 528:case 524:case 639:case 641:Ka(e,Ba(t,2),o),e.i52_const(Na(t,3));break;case 514:case 518:Ka(e,Ba(t,2),o),e.i52_const(Ca(t,3));break;default:Ka(e,Ba(t,2),o)}return 1!==r[0]&&e.appendU8(r[0]),ei(e,Ba(t,1),s),!0}function di(e,t,n,r){const o=133===r?t+6:t+8,s=Fa(n,B(o-2));e.local("pLocals"),e.ptr_const(o),e.appendU8(54),e.appendMemarg(s,0),e.callHandlerReturnAddresses.push(o)}function fi(e,t){const n=o.mono_jiterp_get_opcode_info(t,4),r=e+2+2*o.mono_jiterp_get_opcode_info(t,2);let s;switch(n){case 7:s=O(r);break;case 8:s=M(r);break;case 17:s=M(r+2);break;default:return}return s}function _i(e,t,n,r){const s=r>=227&&r<=270,a=fi(t,r);if("number"!=typeof a)return!1;switch(r){case 132:case 133:case 128:case 129:{const s=132===r||133===r,i=t+2*a;return a<=0?e.backBranchOffsets.indexOf(i)>=0?(e.backBranchTraceLevel>1&&Fe(`0x${t.toString(16)} performing backward branch to 0x${i.toString(16)}`),s&&di(e,t,n,r),e.cfg.branch(i,!0,0),la(9,1),!0):(i<e.cfg.entryIp?(e.backBranchTraceLevel>1||e.cfg.trace>1)&&Fe(`0x${t.toString(16)} ${js(r)} target 0x${i.toString(16)} before start of trace`):(e.backBranchTraceLevel>0||e.cfg.trace>0)&&Fe(`0x${t.toString(16)} ${js(r)} target 0x${i.toString(16)} not found in list `+e.backBranchOffsets.map((e=>"0x"+e.toString(16))).join(", ")),o.mono_jiterp_boost_back_branch_target(i),Ps(e,i,5),la(10,1),!0):(e.branchTargets.add(i),s&&di(e,t,n,r),e.cfg.branch(i,!1,0),!0)}case 145:case 143:case 229:case 227:case 146:case 144:{const n=146===r||144===r;Ka(e,Ba(t,1),n?41:40),143===r||227===r?e.appendU8(69):144===r?e.appendU8(80):146===r&&(e.appendU8(80),e.appendU8(69));break}default:if(void 0===wa[r])throw new Error(`Unsupported relop branch opcode: ${js(r)}`);if(4!==o.mono_jiterp_get_opcode_info(r,1))throw new Error(`Unsupported long branch opcode: ${js(r)}`)}const i=t+2*a;return a<0?e.backBranchOffsets.indexOf(i)>=0?(e.backBranchTraceLevel>1&&Fe(`0x${t.toString(16)} performing conditional backward branch to 0x${i.toString(16)}`),e.cfg.branch(i,!0,s?3:1),la(9,1)):(i<e.cfg.entryIp?(e.backBranchTraceLevel>1||e.cfg.trace>1)&&Fe(`0x${t.toString(16)} ${js(r)} target 0x${i.toString(16)} before start of trace`):(e.backBranchTraceLevel>0||e.cfg.trace>0)&&Fe(`0x${t.toString(16)} ${js(r)} target 0x${i.toString(16)} not found in list `+e.backBranchOffsets.map((e=>"0x"+e.toString(16))).join(", ")),o.mono_jiterp_boost_back_branch_target(i),e.block(64,4),Ps(e,i,5),e.endBlock(),la(10,1)):(e.branchTargets.add(i),e.cfg.branch(i,!1,s?3:1)),!0}function mi(e,t,n,r){const o=wa[r];if(!o)return!1;const s=Array.isArray(o)?o[0]:o,a=ya[s],i=ba[s];if(!a&&!i)return!1;const c=a?a[1]:1===i?43:42;return Ka(e,Ba(t,1),c),a||1===i||e.appendU8(i),Array.isArray(o)&&o[1]?(e.appendU8(o[1]),e.appendLeb(Na(t,2))):Ka(e,Ba(t,2),c),a||1==i||e.appendU8(i),a?e.appendU8(a[0]):(e.i32_const(s),e.callImport("relop_fp")),_i(e,t,n,r)}function hi(e,t,n){let r,o,s,a;const i=Ba(t,1),c=Ba(t,2),l=Ba(t,3),p=ka[n];if(!p)return!1;if(r=p[0],o=p[1],"string"==typeof p[2]?s=p[2]:a=p[2],e.local("pLocals"),r){if(Ka(e,c,o?42:43),a)e.appendU8(a);else{if(!s)throw new Error("internal error");e.callImport(s)}return ei(e,i,o?56:57),!0}if(Ka(e,c,o?42:43),Ka(e,l,o?42:43),a)e.appendU8(a);else{if(!s)throw new Error("internal error");e.callImport(s)}return ei(e,i,o?56:57),!0}function gi(e,t,n){const r=n>=87&&n<=112,o=n>=107&&n<=112,s=n>=95&&n<=106||n>=120&&n<=127||o,a=n>=101&&n<=106||n>=124&&n<=127||o;let i,c,l=-1,p=0,u=1;o?(i=Ba(t,1),c=Ba(t,2),l=Ba(t,3),p=Na(t,4),u=Na(t,5)):s?a?r?(i=Ba(t,1),c=Ba(t,2),p=Na(t,3)):(i=Ba(t,2),c=Ba(t,1),p=Na(t,3)):r?(i=Ba(t,1),c=Ba(t,2),l=Ba(t,3)):(i=Ba(t,3),c=Ba(t,1),l=Ba(t,2)):r?(c=Ba(t,2),i=Ba(t,1)):(c=Ba(t,1),i=Ba(t,2));let d,f=54;switch(n){case 87:case 95:case 101:case 107:d=44;break;case 88:case 96:case 102:case 108:d=45;break;case 89:case 97:case 103:case 109:d=46;break;case 90:case 98:case 104:case 110:d=47;break;case 113:case 120:case 124:d=40,f=58;break;case 114:case 121:case 125:d=40,f=59;break;case 91:case 99:case 105:case 111:case 115:case 122:case 126:case 119:d=40;break;case 93:case 117:d=42,f=56;break;case 94:case 118:d=43,f=57;break;case 92:case 100:case 106:case 112:case 116:case 123:case 127:d=41,f=55;break;default:return!1}const _=Za(e,c,40,!0,!0);return _||si(e,c,t,!1),r?(e.local("pLocals"),_?ut(Za(e,c,40,!1,!0),"Unknown jiterpreter cprop failure"):e.local("cknull_ptr"),o?(Ka(e,l,40),0!==p&&(e.i32_const(p),e.appendU8(106),p=0),1!==u&&(e.i32_const(u),e.appendU8(108)),e.appendU8(106)):s&&l>=0?(Ka(e,l,40),e.appendU8(106)):p<0&&(e.i32_const(p),e.appendU8(106),p=0),e.appendU8(d),e.appendMemarg(p,0),ei(e,i,f)):119===n?(_?ut(Za(e,c,40,!1,!0),"Unknown jiterpreter cprop failure"):e.local("cknull_ptr"),ti(e,i,0),e.callImport("copy_ptr")):(_?ut(Za(e,c,40,!1,!0),"Unknown jiterpreter cprop failure"):e.local("cknull_ptr"),s&&l>=0?(Ka(e,l,40),e.appendU8(106)):p<0&&(e.i32_const(p),e.appendU8(106),p=0),Ka(e,i,d),e.appendU8(f),e.appendMemarg(p,0)),!0}function bi(e,t,n,r,o){e.block(),Ka(e,r,40),e.local("index",34);let s="cknull_ptr";e.options.zeroPageOptimization&&ra()?(la(8,1),Ka(e,n,40),s="src_ptr",e.local(s,34)):si(e,n,t,!0),e.appendU8(40),e.appendMemarg(Ys(9),2),e.appendU8(73),e.appendU8(13),e.appendULeb(0),Ps(e,t,9),e.endBlock(),e.local(s),e.i32_const(Ys(1)),e.appendU8(106),e.local("index"),1!=o&&(e.i32_const(o),e.appendU8(108)),e.appendU8(106)}function yi(e,t,n,r){const o=r<=328&&r>=315||341===r,s=Ba(n,o?2:1),a=Ba(n,o?1:3),i=Ba(n,o?3:2);let c,l,p=54;switch(r){case 341:return e.local("pLocals"),si(e,s,n,!0),e.appendU8(40),e.appendMemarg(Ys(9),2),ei(e,a,54),!0;case 326:return e.local("pLocals"),l=Ba(n,4),bi(e,n,s,i,l),ei(e,a,54),!0;case 337:return e.block(),Ka(e,Ba(n,1),40),Ka(e,Ba(n,2),40),Ka(e,Ba(n,3),40),e.callImport("stelemr_tc"),e.appendU8(13),e.appendULeb(0),Ps(e,n,10),e.endBlock(),!0;case 340:return bi(e,n,s,i,4),ti(e,a,0),e.callImport("copy_ptr"),!0;case 324:case 320:case 319:case 333:l=4,c=40;break;case 315:l=1,c=44;break;case 316:l=1,c=45;break;case 330:case 329:l=1,c=40,p=58;break;case 317:l=2,c=46;break;case 318:l=2,c=47;break;case 332:case 331:l=2,c=40,p=59;break;case 322:case 335:l=4,c=42,p=56;break;case 321:case 334:l=8,c=41,p=55;break;case 323:case 336:l=8,c=43,p=57;break;case 325:{const t=Ba(n,4);return e.local("pLocals"),e.i32_const(Ba(n,1)),e.appendU8(106),bi(e,n,s,i,t),Js(e,t),Xa(Ba(n,1),t),!0}case 338:{const r=Ba(n,5),o=Da(t,Ba(n,4));return bi(e,n,s,i,r),ti(e,a,0),e.ptr_const(o),e.callImport("value_copy"),!0}case 339:{const t=Ba(n,5);return bi(e,n,s,i,t),ti(e,a,0),Js(e,t),!0}default:return!1}return o?(e.local("pLocals"),bi(e,n,s,i,l),e.appendU8(c),e.appendMemarg(0,0),ei(e,a,p)):(bi(e,n,s,i,l),Ka(e,a,c),e.appendU8(p),e.appendMemarg(0,0)),!0}function wi(){return void 0!==Wa||(Wa=!0===ot.featureWasmSimd,Wa||Fe("Disabling Jiterpreter SIMD")),Wa}function ki(e,t,n){const r=`${t}_${n.toString(16)}`;return"object"!=typeof e.importedFunctions[r]&&e.defineImportedFunction("s",r,t,!1,n),r}function Si(e,t,n,r,s,a){if(e.options.enableSimd&&wi())switch(s){case 2:if(function(e,t,n){const r=o.mono_jiterp_get_simd_opcode(1,n);if(r>=0)return ja.has(n)?(e.local("pLocals"),Ka(e,Ba(t,2),40),e.appendSimd(r,!0),e.appendMemarg(0,0),vi(e,t)):(Ui(e,t),e.appendSimd(r),vi(e,t)),!0;const s=La[n];if(s)return Ui(e,t),e.appendSimd(s),ei(e,Ba(t,1),54),!0;switch(n){case 6:case 7:case 8:case 9:{const r=Ra[n];return e.local("pLocals"),e.v128_const(0),Ka(e,Ba(t,2),r[0]),e.appendSimd(r[1]),e.appendU8(0),ei(e,Ba(t,1),253,11),!0}case 14:return Ui(e,t,7),vi(e,t),!0;case 15:return Ui(e,t,8),vi(e,t),!0;case 16:return Ui(e,t,9),vi(e,t),!0;case 17:return Ui(e,t,10),vi(e,t),!0;default:return!1}}(e,t,a))return!0;break;case 3:if(function(e,t,n){const r=o.mono_jiterp_get_simd_opcode(2,n);if(r>=0){const o=xa.has(n),s=Ia[n];if(o)e.local("pLocals"),Ka(e,Ba(t,2),253,0),Ka(e,Ba(t,3),40),e.appendSimd(r),vi(e,t);else if(Array.isArray(s)){const n=za(e,Ba(t,3)),o=s[0];if("number"!=typeof n)return Pe(`${e.functions[0].name}: Non-constant lane index passed to ExtractScalar`),!1;if(n>=o||n<0)return Pe(`${e.functions[0].name}: ExtractScalar index ${n} out of range (0 - ${o-1})`),!1;e.local("pLocals"),Ka(e,Ba(t,2),253,0),e.appendSimd(r),e.appendU8(n),ei(e,Ba(t,1),s[1])}else Ei(e,t),e.appendSimd(r),vi(e,t);return!0}switch(n){case 191:return Ka(e,Ba(t,2),40),Ka(e,Ba(t,3),253,0),e.appendSimd(11),e.appendMemarg(0,0),!0;case 10:case 11:return Ei(e,t),e.appendSimd(214),e.appendSimd(195),11===n&&e.appendU8(69),ei(e,Ba(t,1),54),!0;case 12:case 13:{const r=13===n,o=r?71:65;return e.local("pLocals"),Ka(e,Ba(t,2),253,0),e.local("math_lhs128",34),Ka(e,Ba(t,3),253,0),e.local("math_rhs128",34),e.appendSimd(o),e.local("math_lhs128"),e.local("math_lhs128"),e.appendSimd(o),e.local("math_rhs128"),e.local("math_rhs128"),e.appendSimd(o),e.appendSimd(80),e.appendSimd(77),e.appendSimd(80),e.appendSimd(r?195:163),ei(e,Ba(t,1),54),!0}case 47:{const n=Ba(t,3),r=za(e,n);return e.local("pLocals"),Ka(e,Ba(t,2),253,0),"object"==typeof r?(e.appendSimd(12),e.appendBytes(r)):Ka(e,n,253,0),e.appendSimd(14),vi(e,t),!0}case 48:case 49:return function(e,t,n){const r=16/n,o=Ba(t,3),s=za(e,o);if(2!==r&&4!==r&&ut(!1,"Unsupported shuffle element size"),e.local("pLocals"),Ka(e,Ba(t,2),253,0),"object"==typeof s){const t=new Uint8Array(_c),o=2===r?new Uint16Array(s.buffer,s.byteOffset,n):new Uint32Array(s.buffer,s.byteOffset,n);for(let e=0,s=0;e<n;e++,s+=r){const n=o[e];for(let e=0;e<r;e++)t[s+e]=n*r+e}e.appendSimd(12),e.appendBytes(t)}else{Ka(e,o,253,0),4===n&&(e.v128_const(0),e.appendSimd(134)),e.v128_const(0),e.appendSimd(102),e.appendSimd(12);for(let t=0;t<n;t++)for(let n=0;n<r;n++)e.appendU8(t);e.appendSimd(14),e.i32_const(4===n?2:1),e.appendSimd(107),e.appendSimd(12);for(let t=0;t<n;t++)for(let t=0;t<r;t++)e.appendU8(t);e.appendSimd(80)}return e.appendSimd(14),vi(e,t),!0}(e,t,48===n?8:4);default:return!1}return!1}(e,t,a))return!0;break;case 4:if(function(e,t,n){const r=o.mono_jiterp_get_simd_opcode(3,n);if(r>=0){const o=Aa[n],s=$a[n];if(Array.isArray(o)){const n=o[0],s=za(e,Ba(t,3));if("number"!=typeof s)return Pe(`${e.functions[0].name}: Non-constant lane index passed to ReplaceScalar`),!1;if(s>=n||s<0)return Pe(`${e.functions[0].name}: ReplaceScalar index ${s} out of range (0 - ${n-1})`),!1;e.local("pLocals"),Ka(e,Ba(t,2),253,0),Ka(e,Ba(t,4),o[1]),e.appendSimd(r),e.appendU8(s),vi(e,t)}else if(Array.isArray(s)){const n=s[0],o=za(e,Ba(t,4));if("number"!=typeof o)return Pe(`${e.functions[0].name}: Non-constant lane index passed to store method`),!1;if(o>=n||o<0)return Pe(`${e.functions[0].name}: Store lane ${o} out of range (0 - ${n-1})`),!1;Ka(e,Ba(t,2),40),Ka(e,Ba(t,3),253,0),e.appendSimd(r),e.appendMemarg(0,0),e.appendU8(o)}else!function(e,t){e.local("pLocals"),Ka(e,Ba(t,2),253,0),Ka(e,Ba(t,3),253,0),Ka(e,Ba(t,4),253,0)}(e,t),e.appendSimd(r),vi(e,t);return!0}switch(n){case 0:return e.local("pLocals"),Ka(e,Ba(t,3),253,0),Ka(e,Ba(t,4),253,0),Ka(e,Ba(t,2),253,0),e.appendSimd(82),vi(e,t),!0;case 7:{const n=za(e,Ba(t,4));if("object"!=typeof n)return Pe(`${e.functions[0].name}: Non-constant indices passed to PackedSimd.Shuffle`),!1;for(let t=0;t<32;t++){const r=n[t];if(r<0||r>31)return Pe(`${e.functions[0].name}: Shuffle lane index #${t} (${r}) out of range (0 - 31)`),!1}return e.local("pLocals"),Ka(e,Ba(t,2),253,0),Ka(e,Ba(t,3),253,0),e.appendSimd(13),e.appendBytes(n),vi(e,t),!0}default:return!1}}(e,t,a))return!0}switch(n){case 650:if(e.options.enableSimd&&wi()){e.local("pLocals");const n=Y().slice(t+4,t+4+_c);e.v128_const(n),vi(e,t),Pa.set(Ba(t,1),{type:"v128",value:n})}else ti(e,Ba(t,1),_c),e.ptr_const(t+4),Js(e,_c);return!0;case 651:case 652:case 653:case 654:{const r=Ua[n],o=_c/r,s=Ba(t,1),a=Ba(t,2),i=Ea[n],c=Ta[n];for(let t=0;t<o;t++)e.local("pLocals"),Ka(e,a+t*mc,i),ei(e,s+t*r,c);return!0}case 655:{Fs[r]=(Fs[r]||0)+1,ti(e,Ba(t,1),_c),ti(e,Ba(t,2),0);const n=ki(e,"simd_p_p",o.mono_jiterp_get_simd_intrinsic(1,a));return e.callImport(n),!0}case 656:{Fs[r]=(Fs[r]||0)+1,ti(e,Ba(t,1),_c),ti(e,Ba(t,2),0),ti(e,Ba(t,3),0);const n=ki(e,"simd_p_pp",o.mono_jiterp_get_simd_intrinsic(2,a));return e.callImport(n),!0}case 657:{Fs[r]=(Fs[r]||0)+1,ti(e,Ba(t,1),_c),ti(e,Ba(t,2),0),ti(e,Ba(t,3),0),ti(e,Ba(t,4),0);const n=ki(e,"simd_p_ppp",o.mono_jiterp_get_simd_intrinsic(3,a));return e.callImport(n),!0}default:return Fe(`jiterpreter emit_simd failed for ${r}`),!1}}function vi(e,t){ei(e,Ba(t,1),253,11)}function Ui(e,t,n){e.local("pLocals"),Ka(e,Ba(t,2),253,n||0)}function Ei(e,t){e.local("pLocals"),Ka(e,Ba(t,2),253,0),Ka(e,Ba(t,3),253,0)}function Ti(e,t,n){if(!e.options.enableAtomics)return!1;const r=Sa[n];if(r){const n=r[2]>2;return e.local("pLocals"),si(e,Ba(t,2),t,!0),Ka(e,Ba(t,3),n?41:40),e.appendAtomic(r[0],!1),e.appendMemarg(0,r[2]),0!==r[1]&&e.appendU8(r[1]),ei(e,Ba(t,1),n?55:54),!0}const o=va[n];if(o){const n=o[2]>2;return e.local("pLocals"),si(e,Ba(t,2),t,!0),Ka(e,Ba(t,4),n?41:40),Ka(e,Ba(t,3),n?41:40),e.appendAtomic(o[0],!1),e.appendMemarg(0,o[2]),0!==o[1]&&e.appendU8(o[1]),ei(e,Ba(t,1),n?55:54),!0}return!1}const xi=64;let Ii,Ai,ji,$i=0;const Li={};function Ri(){return Ai||(Ai=[ta("interp_entry_prologue",Zs("mono_jiterp_interp_entry_prologue")),ta("interp_entry",Zs("mono_jiterp_interp_entry")),ta("unbox",Zs("mono_jiterp_object_unbox")),ta("stackval_from_data",Zs("mono_jiterp_stackval_from_data"))],Ai)}let Bi,Ni=class{constructor(e,t,n,r,o,s,a,i){this.imethod=e,this.method=t,this.argumentCount=n,this.unbox=o,this.hasThisReference=s,this.hasReturnValue=a,this.paramTypes=new Array(n);for(let e=0;e<n;e++)this.paramTypes[e]=D(r+4*e);this.defaultImplementation=i,this.result=0,this.hitCount=0}generateName(){const e=o.mono_wasm_method_get_full_name(this.method);try{const t=xe(e);this.name=t;let n=t;if(n){const e=24;n.length>e&&(n=n.substring(n.length-e,n.length)),n=`${this.imethod.toString(16)}_${n}`}else n=`${this.imethod.toString(16)}_${this.hasThisReference?"i":"s"}${this.hasReturnValue?"_r":""}_${this.argumentCount}`;this.traceName=n}finally{e&&Xe._free(e)}}getTraceName(){return this.traceName||this.generateName(),this.traceName||"unknown"}getName(){return this.name||this.generateName(),this.name||"unknown"}};function Ci(){const e=[];let t=0;for(;0!=(t=o.mono_jiterp_tlqueue_next(1));){const n=Li[t];n?e.push(n):Fe(`Failed to find corresponding info for method ptr ${t} from jit queue!`)}if(!e.length)return;const n=4*e.length+1;let r=Ii;if(r?r.clear(n):(Ii=r=new Ns(n),r.defineType("unbox",{pMonoObject:127},127,!0),r.defineType("interp_entry_prologue",{pData:127,this_arg:127},127,!0),r.defineType("interp_entry",{pData:127,res:127},64,!0),r.defineType("stackval_from_data",{type:127,result:127,value:127},64,!0)),r.options.wasmBytesLimit<=ca(6))return;const s=Ms();let a=0,i=!0,c=!1;try{r.appendU32(1836278016),r.appendU32(1);for(let t=0;t<e.length;t++){const n=e[t],o={};n.hasThisReference&&(o.this_arg=127),n.hasReturnValue&&(o.res=127);for(let e=0;e<n.argumentCount;e++)o[`arg${e}`]=127;o.rmethod=127,r.defineType(n.getTraceName(),o,64,!1)}r.generateTypeSection();const t=Ri();r.compressImportNames=!0;for(let e=0;e<t.length;e++)t[e]||ut(!1,`trace #${e} missing`),r.defineImportedFunction("i",t[e][0],t[e][1],!0,t[e][2]);for(let e=0;e<t.length;e++)r.markImportAsUsed(t[e][0]);r._generateImportSection(!1),r.beginSection(3),r.appendULeb(e.length);for(let t=0;t<e.length;t++){const n=e[t].getTraceName();r.functionTypes[n]||ut(!1,"func type missing"),r.appendULeb(r.functionTypes[n][0])}r.beginSection(7),r.appendULeb(e.length);for(let t=0;t<e.length;t++){const n=e[t].getTraceName();r.appendName(n),r.appendU8(0),r.appendULeb(r.importedFunctionCount+t)}r.beginSection(10),r.appendULeb(e.length);for(let t=0;t<e.length;t++){const n=e[t],o=n.getTraceName();r.beginFunction(o,{sp_args:127,need_unbox:127,scratchBuffer:127}),Di(r,n),r.appendU8(11),r.endFunction(!0)}r.endSection(),a=Ms();const n=r.getArrayView();la(6,n.length);const o=new WebAssembly.Module(n),s=r.getWasmImports(),c=new WebAssembly.Instance(o,s);for(let t=0;t<e.length;t++){const n=e[t],r=n.getTraceName(),o=c.exports[r];ji.set(n.result,o),i=!1}la(2,e.length)}catch(e){c=!0,i=!1,Pe(`interp_entry code generation failed: ${e}`),Xs()}finally{const t=Ms();if(a?(la(11,a-s),la(12,t-a)):la(11,t-s),c){Fe(`// ${e.length} trampolines generated, blob follows //`);let t="",n=0;try{r.inSection&&r.endSection()}catch(e){}const o=r.getArrayView();for(let e=0;e<o.length;e++){const r=o[e];r<16&&(t+="0"),t+=r.toString(16),t+=" ",t.length%10==0&&(Fe(`${n}\t${t}`),t="",n=e+1)}Fe(`${n}\t${t}`),Fe("// end blob //")}else i&&!c&&Pe("failed to generate trampoline for unknown reason")}}function Oi(e,t,n,r,s){const a=o.mono_jiterp_type_get_raw_value_size(n),i=o.mono_jiterp_get_arg_offset(t,0,s);switch(a){case 256:e.local("sp_args"),e.local(r),e.appendU8(54),e.appendMemarg(i,2);break;case-1:case-2:case 1:case 2:case 4:switch(e.local("sp_args"),e.local(r),a){case-1:e.appendU8(45),e.appendMemarg(0,0);break;case 1:e.appendU8(44),e.appendMemarg(0,0);break;case-2:e.appendU8(47),e.appendMemarg(0,0);break;case 2:e.appendU8(46),e.appendMemarg(0,0);break;case 4:e.appendU8(40),e.appendMemarg(0,2)}e.appendU8(54),e.appendMemarg(i,2);break;default:e.ptr_const(n),e.local("sp_args"),e.i32_const(i),e.appendU8(106),e.local(r),e.callImport("stackval_from_data")}}function Di(e,t){const n=Xe._malloc(xi);_(n,xi),v(n+Ys(13),t.paramTypes.length+(t.hasThisReference?1:0)),t.hasThisReference&&(e.block(),e.local("rmethod"),e.i32_const(1),e.appendU8(113),e.appendU8(69),e.appendU8(13),e.appendULeb(0),e.local("this_arg"),e.callImport("unbox"),e.local("this_arg",33),e.endBlock()),e.ptr_const(n),e.local("scratchBuffer",34),e.local("rmethod"),e.i32_const(-2),e.appendU8(113),e.appendU8(54),e.appendMemarg(Ys(6),0),e.local("scratchBuffer"),t.hasThisReference?e.local("this_arg"):e.i32_const(0),e.callImport("interp_entry_prologue"),e.local("sp_args",33),t.hasThisReference&&Oi(e,t.imethod,0,"this_arg",0);for(let n=0;n<t.paramTypes.length;n++){const r=t.paramTypes[n];Oi(e,t.imethod,r,`arg${n}`,n+(t.hasThisReference?1:0))}return e.local("scratchBuffer"),t.hasReturnValue?e.local("res"):e.i32_const(0),e.callImport("interp_entry"),e.appendU8(15),!0}const Fi=16,Mi=0;let Pi,Vi,zi,Hi=0;const Wi=[],qi={},Gi={};class Ji{constructor(e,t,n,r,s){this.queue=[],r||ut(!1,"Expected nonzero arg_offsets pointer"),this.method=e,this.rmethod=t,this.catchExceptions=s,this.cinfo=n,this.addr=D(n+0),this.wrapper=D(n+8),this.signature=D(n+12),this.noWrapper=0!==R(n+28),this.hasReturnValue=-1!==O(n+24),this.returnType=o.mono_jiterp_get_signature_return_type(this.signature),this.paramCount=o.mono_jiterp_get_signature_param_count(this.signature),this.hasThisReference=0!==o.mono_jiterp_get_signature_has_this(this.signature);const a=o.mono_jiterp_get_signature_params(this.signature);this.paramTypes=new Array(this.paramCount);for(let e=0;e<this.paramCount;e++)this.paramTypes[e]=D(a+4*e);const i=this.paramCount+(this.hasThisReference?1:0);this.argOffsets=new Array(this.paramCount);for(let e=0;e<i;e++)this.argOffsets[e]=D(r+4*e);this.target=this.noWrapper?this.addr:this.wrapper,this.result=0,this.wasmNativeReturnType=this.returnType&&this.hasReturnValue?Yi[o.mono_jiterp_type_to_stind(this.returnType)]:64,this.wasmNativeSignature=this.paramTypes.map((e=>Yi[o.mono_jiterp_type_to_ldind(e)])),this.enableDirect=pa().directJitCalls&&!this.noWrapper&&this.wasmNativeReturnType&&(0===this.wasmNativeSignature.length||this.wasmNativeSignature.every((e=>e))),this.enableDirect&&(this.target=this.addr);let c=this.target.toString(16);const l=Hi++;this.name=`${this.enableDirect?"jcp":"jcw"}_${c}_${l.toString(16)}`}}function Xi(e){let t=Wi[e];return t||(e>=Wi.length&&(Wi.length=e+1),Vi||(Vi=zs()),Wi[e]=t=Vi.get(e)),t}function Qi(){const e=[];let t=0;for(;0!=(t=o.mono_jiterp_tlqueue_next(0));){const n=Gi[t];if(n)for(let t=0;t<n.length;t++)0===n[t].result&&e.push(n[t]);else Fe(`Failed to find corresponding info list for method ptr ${t} from jit queue!`)}if(!e.length)return;let n=Pi;if(n?n.clear(0):(Pi=n=new Ns(0),n.defineType("trampoline",{ret_sp:127,sp:127,ftndesc:127,thrown:127},64,!0),n.defineType("begin_catch",{ptr:127},64,!0),n.defineType("end_catch",{},64,!0),n.defineImportedFunction("i","begin_catch","begin_catch",!0,Zs("mono_jiterp_begin_catch")),n.defineImportedFunction("i","end_catch","end_catch",!0,Zs("mono_jiterp_end_catch"))),n.options.wasmBytesLimit<=ca(6))return void o.mono_jiterp_tlqueue_clear(0);n.options.enableWasmEh&&(void 0!==zi||(zi=!0===ot.featureWasmEh,zi||Fe("Disabling Jiterpreter Exception Handling")),zi||(ia({enableWasmEh:!1}),n.options.enableWasmEh=!1));const r=Ms();let s=0,a=!0,i=!1;const c=[];try{Vi||(Vi=zs()),n.appendU32(1836278016),n.appendU32(1);for(let t=0;t<e.length;t++){const r=e[t],o={};if(r.enableDirect){r.hasThisReference&&(o.this=127);for(let e=0;e<r.wasmNativeSignature.length;e++)o[`arg${e}`]=r.wasmNativeSignature[e];o.rgctx=127}else{const e=(r.hasThisReference?1:0)+(r.hasReturnValue?1:0)+r.paramCount;for(let t=0;t<e;t++)o[`arg${t}`]=127;o.ftndesc=127}n.defineType(r.name,o,r.enableDirect?r.wasmNativeReturnType:64,!1);const s=Xi(r.target);"function"!=typeof s&&ut(!1,`expected call target to be function but was ${s}`),c.push([r.name,r.name,s])}n.generateTypeSection(),n.compressImportNames=!0;for(let e=0;e<c.length;e++)n.defineImportedFunction("i",c[e][0],c[e][1],!1,c[e][2]);for(let e=0;e<c.length;e++)n.markImportAsUsed(c[e][0]);n.markImportAsUsed("begin_catch"),n.markImportAsUsed("end_catch"),n._generateImportSection(!1),n.beginSection(3),n.appendULeb(e.length),n.functionTypes.trampoline||ut(!1,"func type missing");for(let t=0;t<e.length;t++)n.appendULeb(n.functionTypes.trampoline[0]);n.beginSection(7),n.appendULeb(e.length);for(let t=0;t<e.length;t++){const r=e[t];n.appendName(r.name),n.appendU8(0),n.appendULeb(n.importedFunctionCount+t)}n.beginSection(10),n.appendULeb(e.length);for(let t=0;t<e.length;t++){const r=e[t];if(n.beginFunction("trampoline",{old_sp:127}),!tc(n,r))throw new Error(`Failed to generate ${r.name}`);n.appendU8(11),n.endFunction(!0)}n.endSection(),s=Ms();const t=n.getArrayView();la(6,t.length);const r=new WebAssembly.Module(t),i=n.getWasmImports(),l=new WebAssembly.Instance(r,i);for(let t=0;t<e.length;t++){const n=e[t],r=Hs(1,l.exports[n.name]);if(n.result=r,r>0){o.mono_jiterp_register_jit_call_thunk(n.cinfo,r);for(let e=0;e<n.queue.length;e++)o.mono_jiterp_register_jit_call_thunk(n.queue[e],r);n.enableDirect&&la(4,1),la(3,1)}n.queue.length=0,a=!1}}catch(e){i=!0,a=!1,Pe(`jit_call code generation failed: ${e}`),Xs()}finally{const t=Ms();if(s?(la(11,s-r),la(12,t-s)):la(11,t-r),i||a)for(let t=0;t<e.length;t++)e[t].result=-1;if(i){Fe(`// ${e.length} jit call wrappers generated, blob follows //`);for(let t=0;t<e.length;t++)Fe(`// #${t} === ${e[t].name} hasThis=${e[t].hasThisReference} hasRet=${e[t].hasReturnValue} wasmArgTypes=${e[t].wasmNativeSignature}`);let t="",r=0;try{n.inSection&&n.endSection()}catch(e){}const o=n.getArrayView();for(let e=0;e<o.length;e++){const n=o[e];n<16&&(t+="0"),t+=n.toString(16),t+=" ",t.length%10==0&&(Fe(`${r}\t${t}`),t="",r=e+1)}Fe(`${r}\t${t}`),Fe("// end blob //")}else a&&!i&&Pe("failed to generate trampoline for unknown reason")}}const Yi={65535:127,70:127,71:127,72:127,73:127,74:127,75:127,76:126,77:127,78:125,79:124,80:127,81:127,82:127,83:127,84:127,85:126,86:125,87:124,223:127},Zi={70:44,71:45,72:46,73:47,74:40,75:40,76:41,77:40,78:42,79:43,80:40,81:54,82:58,83:59,84:54,85:55,86:56,87:57,223:54};function Ki(e,t,n){e.local("sp"),e.appendU8(n),e.appendMemarg(t,0)}function ec(e,t){e.local("sp"),e.i32_const(t),e.appendU8(106)}function tc(e,t){let n=0;e.options.enableWasmEh&&e.block(64,6),t.hasReturnValue&&t.enableDirect&&e.local("ret_sp"),t.hasThisReference&&(Ki(e,t.argOffsets[0],40),n++),t.hasReturnValue&&!t.enableDirect&&e.local("ret_sp");for(let r=0;r<t.paramCount;r++){const s=t.argOffsets[n+r];if(R(D(t.cinfo+Fi)+r)==Mi)Ki(e,s,40);else if(t.enableDirect){const n=o.mono_jiterp_type_to_ldind(t.paramTypes[r]);if(n||ut(!1,`No load opcode for ${t.paramTypes[r]}`),65535===n)ec(e,s);else{const o=Zi[n];if(!o)return Pe(`No wasm load op for arg #${r} type ${t.paramTypes[r]} cil opcode ${n}`),!1;Ki(e,s,o)}}else ec(e,s)}if(e.local("ftndesc"),(t.enableDirect||t.noWrapper)&&(e.appendU8(40),e.appendMemarg(4,0)),e.callImport(t.name),t.hasReturnValue&&t.enableDirect){const n=o.mono_jiterp_type_to_stind(t.returnType),r=Zi[n];if(!r)return Pe(`No wasm store op for return type ${t.returnType} cil opcode ${n}`),!1;e.appendU8(r),e.appendMemarg(0,0)}return e.options.enableWasmEh&&(e.appendU8(7),e.appendULeb(e.getTypeIndex("__cpp_exception")),e.callImport("begin_catch"),e.callImport("end_catch"),e.local("thrown"),e.i32_const(1),e.appendU8(54),e.appendMemarg(0,2),e.endBlock()),e.appendU8(15),!0}const nc=30;let rc,oc;const sc=[],ac=[];class ic{constructor(e){this.name=e,this.eip=0}}class cc{constructor(e,t,n){this.ip=e,this.index=t,this.isVerbose=!!n}get hitCount(){return o.mono_jiterp_get_trace_hit_count(this.index)}}const lc={};let pc=1;const uc={},dc={},fc=4,_c=16,mc=8;let hc,gc;const bc=["asin","acos","atan","asinh","acosh","atanh","cos","sin","tan","cosh","sinh","tanh","exp","log","log2","log10","cbrt"],yc=["fmod","atan2","pow"],wc=["asinf","acosf","atanf","asinhf","acoshf","atanhf","cosf","sinf","tanf","coshf","sinhf","tanhf","expf","logf","log2f","log10f","cbrtf"],kc=["fmodf","atan2f","powf"];function Sc(e,t,n){if(o.mono_jiterp_trace_bailout(n),14===n)return e;const r=dc[t];if(!r)return void Pe(`trace info not found for ${t}`);let s=r.bailoutCounts;s||(r.bailoutCounts=s={});const a=s[n];return s[n]=a?a+1:1,r.bailoutCount?r.bailoutCount++:r.bailoutCount=1,e}function vc(){if(gc)return gc;gc=[ta("bailout",Sc),ta("copy_ptr",Zs("mono_wasm_copy_managed_pointer")),ta("entry",Zs("mono_jiterp_increase_entry_count")),ta("value_copy",Zs("mono_jiterp_value_copy")),ta("gettype",Zs("mono_jiterp_gettype_ref")),ta("castv2",Zs("mono_jiterp_cast_v2")),ta("hasparent",Zs("mono_jiterp_has_parent_fast")),ta("imp_iface",Zs("mono_jiterp_implements_interface")),ta("imp_iface_s",Zs("mono_jiterp_implements_special_interface")),ta("box",Zs("mono_jiterp_box_ref")),ta("localloc",Zs("mono_jiterp_localloc")),["ckovr_i4","overflow_check_i4",Zs("mono_jiterp_overflow_check_i4")],["ckovr_u4","overflow_check_i4",Zs("mono_jiterp_overflow_check_u4")],ta("newobj_i",Zs("mono_jiterp_try_newobj_inlined")),ta("newstr",Zs("mono_jiterp_try_newstr")),ta("ld_del_ptr",Zs("mono_jiterp_ld_delegate_method_ptr")),ta("ldtsflda",Zs("mono_jiterp_ldtsflda")),ta("conv",Zs("mono_jiterp_conv")),ta("relop_fp",Zs("mono_jiterp_relop_fp")),ta("safepoint",Zs("mono_jiterp_do_safepoint")),ta("hashcode",Zs("mono_jiterp_get_hashcode")),ta("try_hash",Zs("mono_jiterp_try_get_hashcode")),ta("hascsize",Zs("mono_jiterp_object_has_component_size")),ta("hasflag",Zs("mono_jiterp_enum_hasflag")),ta("array_rank",Zs("mono_jiterp_get_array_rank")),["a_elesize","array_rank",Zs("mono_jiterp_get_array_element_size")],ta("stfld_o",Zs("mono_jiterp_set_object_field")),["stelemr_tc","stelemr",Zs("mono_jiterp_stelem_ref")],ta("fma",Zs("fma")),ta("fmaf",Zs("fmaf"))],ac.length>0&&(gc.push(["trace_eip","trace_eip",Uc]),gc.push(["trace_args","trace_eip",Ec]));const e=(e,t)=>{for(let n=0;n<e.length;n++){const r=e[n];gc.push([r,t,Zs(r)])}};return e(wc,"mathop_f_f"),e(kc,"mathop_ff_f"),e(bc,"mathop_d_d"),e(yc,"mathop_dd_d"),gc}function Uc(e,t){const n=lc[e];if(!n)throw new Error(`Unrecognized instrumented trace id ${e}`);n.eip=t,rc=n}function Ec(e,t){if(!rc)throw new Error("No trace active");rc.operand1=e>>>0,rc.operand2=t>>>0}function Tc(e,t,n,r){if("number"==typeof r)o.mono_jiterp_adjust_abort_count(r,1),r=js(r);else{let e=uc[r];"number"!=typeof e?e=1:e++,uc[r]=e}dc[e].abortReason=r}function xc(e){if(!ot.runtimeReady)return;if(oc||(oc=pa()),!oc.enableStats)return;const t=ca(9),n=ca(10),r=ca(7),s=ca(8),a=ca(3),i=ca(4),c=ca(2),l=ca(1),p=ca(0),u=ca(6),d=ca(11),f=ca(12),_=t/(t+n)*100,m=o.mono_jiterp_get_rejected_trace_count(),h=oc.eliminateNullChecks?r.toString():"off",g=oc.zeroPageOptimization?s.toString()+(ra()?"":" (disabled)"):"off",b=oc.enableBackwardBranches?`emitted: ${t}, failed: ${n} (${_.toFixed(1)}%)`:": off",y=a?oc.directJitCalls?`direct jit calls: ${i} (${(i/a*100).toFixed(1)}%)`:"direct jit calls: off":"";if(Fe(`// jitted ${u} bytes; ${l} traces (${(l/p*100).toFixed(1)}%) (${m} rejected); ${a} jit_calls; ${c} interp_entries`),Fe(`// cknulls eliminated: ${h}, fused: ${g}; back-branches ${b}; ${y}`),Fe(`// time: ${0|d}ms generating, ${0|f}ms compiling wasm.`),!e){if(oc.countBailouts){const e=Object.values(dc);e.sort(((e,t)=>(t.bailoutCount||0)-(e.bailoutCount||0)));for(let e=0;e<fa.length;e++){const t=o.mono_jiterp_get_trace_bailout_count(e);t&&Fe(`// traces bailed out ${t} time(s) due to ${fa[e]}`)}for(let t=0,n=0;t<e.length&&n<nc;t++){const r=e[t];if(r.bailoutCount){n++,Fe(`${r.name}: ${r.bailoutCount} bailout(s)`);for(const e in r.bailoutCounts)Fe(`  ${fa[e]} x${r.bailoutCounts[e]}`)}}}if(oc.estimateHeat){const e={},t=Object.values(dc);for(let n=0;n<t.length;n++){const r=t[n];r.abortReason&&"end-of-body"!==r.abortReason&&(e[r.abortReason]?e[r.abortReason]+=r.hitCount:e[r.abortReason]=r.hitCount)}t.sort(((e,t)=>t.hitCount-e.hitCount)),Fe("// hottest failed traces:");for(let e=0,n=0;e<t.length&&n<nc;e++)if(t[e].name&&!(t[e].fnPtr||t[e].name.indexOf("Xunit.")>=0)){if(t[e].abortReason){if(t[e].abortReason.startsWith("mono_icall_")||t[e].abortReason.startsWith("ret."))continue;switch(t[e].abortReason){case"trace-too-small":case"trace-too-big":case"call":case"callvirt.fast":case"calli.nat.fast":case"calli.nat":case"call.delegate":case"newobj":case"newobj_vt":case"newobj_slow":case"switch":case"rethrow":case"end-of-body":case"ret":case"intrins_marvin_block":case"intrins_ascii_chars_to_uppercase":continue}}n++,Fe(`${t[e].name} @${t[e].ip} (${t[e].hitCount} hits) ${t[e].abortReason}`)}const n=[];for(const t in e)n.push([t,e[t]]);n.sort(((e,t)=>t[1]-e[1])),Fe("// heat:");for(let e=0;e<n.length;e++)Fe(`// ${n[e][0]}: ${n[e][1]}`)}else{for(let e=0;e<689;e++){const t=js(e),n=o.mono_jiterp_adjust_abort_count(e,0);n>0?uc[t]=n:delete uc[t]}const e=Object.keys(uc);e.sort(((e,t)=>uc[t]-uc[e]));for(let t=0;t<e.length;t++)Fe(`// ${e[t]}: ${uc[e[t]]} abort(s)`)}for(const e in Fs)Fe(`// simd ${e}: ${Fs[e]} fallback insn(s)`)}}const Ic="https://dotnet.generated.invalid/interp_pgo";async function Ac(){if(!st.is_runtime_running())return void Fe("Skipped saving interp_pgo table (already exited)");const e=await Lc(Ic);if(e)try{const t=o.mono_interp_pgo_save_table(0,0);if(t<=0)return void Fe("Failed to save interp_pgo table (No data to save)");const r=Xe._malloc(t);if(0!==o.mono_interp_pgo_save_table(r,t))return void Pe("Failed to save interp_pgo table (Unknown error)");const s=Y().slice(r,r+t);await async function(e,t,r){try{const r=await $c();if(!r)return!1;const o=n?new Uint8Array(t).slice(0):t,s=new Response(o,{headers:{"content-type":"application/octet-stream","content-length":t.byteLength.toString()}});return await r.put(e,s),!0}catch(t){return Me("Failed to store entry to the cache: "+e,t),!1}}(e,s)&&Fe("Saved interp_pgo table to cache"),async function(e,t){try{const n=await $c();if(!n)return;const r=await n.keys();for(const o of r)o.url&&o.url!==t&&o.url.startsWith(e)&&await n.delete(o)}catch(e){return}}(Ic,e),Xe._free(r)}catch(e){Pe(`Failed to save interp_pgo table: ${e}`)}else Pe("Failed to save interp_pgo table (No cache key)")}async function jc(){const e=await Lc(Ic);if(!e)return void Pe("Failed to create cache key for interp_pgo table");const t=await async function(e){try{const t=await $c();if(!t)return;const n=await t.match(e);if(!n)return;return n.arrayBuffer()}catch(t){return void Me("Failed to load entry from the cache: "+e,t)}}(e);if(!t)return void Fe("Failed to load interp_pgo table (No table found in cache)");const n=Xe._malloc(t.byteLength);Y().set(new Uint8Array(t),n),o.mono_interp_pgo_load_table(n,t.byteLength)&&Pe("Failed to load interp_pgo table (Unknown error)"),Xe._free(n)}async function $c(){if(tt&&!1===globalThis.window.isSecureContext)return Me("Failed to open the cache, running on an insecure origin"),null;if(void 0===globalThis.caches)return Me("Failed to open the cache, probably running on an insecure origin"),null;const e=`dotnet-resources${document.baseURI.substring(document.location.origin.length)}`;try{return await globalThis.caches.open(e)||null}catch(e){return Me("Failed to open cache"),null}}async function Lc(t){if(!ot.subtle)return null;const n=Object.assign({},ot.config);n.resourcesHash=n.resources.hash,delete n.assets,delete n.resources,n.preferredIcuAsset=st.preferredIcuAsset,delete n.forwardConsoleLogsToWS,delete n.diagnosticTracing,delete n.appendElementOnExit,delete n.interopCleanupOnExit,delete n.dumpThreadsOnNonZeroExit,delete n.logExitCode,delete n.pthreadPoolInitialSize,delete n.pthreadPoolUnusedSize,delete n.asyncFlushOnExit,delete n.remoteSources,delete n.ignorePdbLoadErrors,delete n.maxParallelDownloads,delete n.enableDownloadRetry,delete n.extensions,delete n.runtimeId,delete n.jsThreadBlockingMode,n.GitHash=st.gitHash,n.ProductVersion=e;const r=JSON.stringify(n),o=await ot.subtle.digest("SHA-256",(new TextEncoder).encode(r)),s=new Uint8Array(o);return`${t}-${Array.from(s).map((e=>e.toString(16).padStart(2,"0"))).join("")}`}async function Rc(e){const t=st.config.resources.lazyAssembly;if(!t)throw new Error("No assemblies have been marked as lazy-loadable. Use the 'BlazorWebAssemblyLazyLoad' item group in your project file to enable lazy loading an assembly.");let n=e;e.endsWith(".dll")?n=e.substring(0,e.length-4):e.endsWith(".wasm")&&(n=e.substring(0,e.length-5));const r=n+".dll",o=n+".wasm";if(st.config.resources.fingerprinting){const t=st.config.resources.fingerprinting;for(const n in t){const s=t[n];if(s==r||s==o){e=n;break}}}if(!t[e])if(t[r])e=r;else{if(!t[o])throw new Error(`${e} must be marked with 'BlazorWebAssemblyLazyLoad' item group in your project file to allow lazy-loading.`);e=o}const s={name:e,hash:t[e],behavior:"assembly"};if(st.loadedAssemblies.includes(e))return!1;let a=n+".pdb",i=!1;if(0!=st.config.debugLevel&&(i=Object.prototype.hasOwnProperty.call(t,a),st.config.resources.fingerprinting)){const e=st.config.resources.fingerprinting;for(const t in e)if(e[t]==a){a=t,i=!0;break}}const c=st.retrieve_asset_download(s);let l=null,p=null;if(i){const e=t[a]?st.retrieve_asset_download({name:a,hash:t[a],behavior:"pdb"}):Promise.resolve(null),[n,r]=await Promise.all([c,e]);l=new Uint8Array(n),p=r?new Uint8Array(r):null}else{const e=await c;l=new Uint8Array(e),p=null}return function(e,t){st.assert_runtime_running();const n=Xe.stackSave();try{const n=xn(4),r=In(n,2),o=In(n,3);Mn(r,21),Mn(o,21),yo(r,e,4),yo(o,t,4),gn(mn.LoadLazyAssembly,n)}finally{Xe.stackRestore(n)}}(l,p),!0}async function Bc(e){const t=st.config.resources.satelliteResources;t&&await Promise.all(e.filter((e=>Object.prototype.hasOwnProperty.call(t,e))).map((e=>{const n=[];for(const r in t[e]){const o={name:r,hash:t[e][r],behavior:"resource",culture:e};n.push(st.retrieve_asset_download(o))}return n})).reduce(((e,t)=>e.concat(t)),new Array).map((async e=>{const t=await e;!function(e){st.assert_runtime_running();const t=Xe.stackSave();try{const t=xn(3),n=In(t,2);Mn(n,21),yo(n,e,4),gn(mn.LoadSatelliteAssembly,t)}finally{Xe.stackRestore(t)}}(new Uint8Array(t))})))}function Nc(e){if(e===c)return null;const t=o.mono_wasm_read_as_bool_or_null_unsafe(e);return 0!==t&&(1===t||null)}var Cc,Oc;function Dc(e){if(e)try{(e=e.toLocaleLowerCase()).includes("zh")&&(e=e.replace("chs","HANS").replace("cht","HANT"));const t=Intl.getCanonicalLocales(e.replace("_","-"));return t.length>0?t[0]:void 0}catch(e){return}}!function(e){e[e.Sending=0]="Sending",e[e.Closed=1]="Closed",e[e.Error=2]="Error"}(Cc||(Cc={})),function(e){e[e.Idle=0]="Idle",e[e.PartialCommand=1]="PartialCommand",e[e.Error=2]="Error"}(Oc||(Oc={}));const Fc=[function(e){qo&&(globalThis.clearTimeout(qo),qo=void 0),qo=Xe.safeSetTimeout(mono_wasm_schedule_timer_tick,e)},function(e,t,n,r,o){if(!0!==ot.mono_wasm_runtime_is_ready)return;const s=Y(),a=0!==e?xe(e).concat(".dll"):"",i=dt(new Uint8Array(s.buffer,t,n));let c;r&&(c=dt(new Uint8Array(s.buffer,r,o))),It({eventName:"AssemblyLoaded",assembly_name:a,assembly_b64:i,pdb_b64:c})},function(e,t){const n=xe(t);Qe.logging&&"function"==typeof Qe.logging.debugger&&Qe.logging.debugger(e,n)},function(e,t,n,r){const o={res_ok:e,res:{id:t,value:dt(new Uint8Array(Y().buffer,n,r))}};_t.has(t)&&Me(`Adding an id (${t}) that already exists in commands_received`),_t.set(t,o)},function mono_wasm_fire_debugger_agent_message_with_data(e,t){mono_wasm_fire_debugger_agent_message_with_data_to_pause(dt(new Uint8Array(Y().buffer,e,t)))},mono_wasm_fire_debugger_agent_message_with_data_to_pause,function(){++Jo,Xe.safeSetTimeout(Yo,0)},function(e,t,n,r,s,a,i,c){if(n||ut(!1,"expected instruction pointer"),oc||(oc=pa()),!oc.enableTraces)return 1;if(oc.wasmBytesLimit<=ca(6))return 1;let l,p=dc[r];if(p||(dc[r]=p=new cc(n,r,i)),la(0,1),oc.estimateHeat||ac.length>0||p.isVerbose){const e=o.mono_wasm_method_get_full_name(t);l=xe(e),Xe._free(e)}const u=xe(o.mono_wasm_method_get_name(t));p.name=l||u;let d=oc.noExitBackwardBranches?function(e,t,n){const r=t+n,s=[],a=(e-t)/2;for(;e<r;){const n=(e-t)/2,r=B(e);if(271===r)break;const i=o.mono_jiterp_get_opcode_info(r,1),c=fi(e,r);if("number"==typeof c){if(0===c){Fe(`opcode @${e} branch target is self. aborting backbranch table generation`);break}if(c<0){const t=n+c;if(t<0){Fe(`opcode @${e}'s displacement of ${c} goes before body: ${t}. aborting backbranch table generation`);break}t>=a&&s.push(t)}switch(r){case 132:case 133:s.push(n+i)}e+=2*i}else e+=2*i}return s.length<=0?null:new Uint16Array(s)}(n,s,a):null;if(d&&n!==s){const e=(n-s)/2;let t=!1;for(let n=0;n<d.length;n++)if(d[n]>=e){t=!0;break}t||(d=null)}const f=function(e,t,n,r,s,a,i,c,l){let p=hc;p?p.clear(8):(hc=p=new Ns(8),function(e){e.defineType("trace",{frame:127,pLocals:127,cinfo:127,ip:127},127,!0),e.defineType("bailout",{retval:127,base:127,reason:127},127,!0),e.defineType("copy_ptr",{dest:127,src:127},64,!0),e.defineType("value_copy",{dest:127,src:127,klass:127},64,!0),e.defineType("entry",{imethod:127},127,!0),e.defineType("strlen",{ppString:127,pResult:127},127,!0),e.defineType("getchr",{ppString:127,pIndex:127,pResult:127},127,!0),e.defineType("getspan",{destination:127,span:127,index:127,element_size:127},127,!0),e.defineType("overflow_check_i4",{lhs:127,rhs:127,opcode:127},127,!0),e.defineType("mathop_d_d",{value:124},124,!0),e.defineType("mathop_dd_d",{lhs:124,rhs:124},124,!0),e.defineType("mathop_f_f",{value:125},125,!0),e.defineType("mathop_ff_f",{lhs:125,rhs:125},125,!0),e.defineType("fmaf",{x:125,y:125,z:125},125,!0),e.defineType("fma",{x:124,y:124,z:124},124,!0),e.defineType("trace_eip",{traceId:127,eip:127},64,!0),e.defineType("newobj_i",{ppDestination:127,vtable:127},127,!0),e.defineType("newstr",{ppDestination:127,length:127},127,!0),e.defineType("localloc",{destination:127,len:127,frame:127},64,!0),e.defineType("ld_del_ptr",{ppDestination:127,ppSource:127},64,!0),e.defineType("ldtsflda",{ppDestination:127,offset:127},64,!0),e.defineType("gettype",{destination:127,source:127},127,!0),e.defineType("castv2",{destination:127,source:127,klass:127,opcode:127},127,!0),e.defineType("hasparent",{klass:127,parent:127},127,!0),e.defineType("imp_iface",{vtable:127,klass:127},127,!0),e.defineType("imp_iface_s",{obj:127,vtable:127,klass:127},127,!0),e.defineType("box",{vtable:127,destination:127,source:127,vt:127},64,!0),e.defineType("conv",{destination:127,source:127,opcode:127},127,!0),e.defineType("relop_fp",{lhs:124,rhs:124,opcode:127},127,!0),e.defineType("safepoint",{frame:127,ip:127},64,!0),e.defineType("hashcode",{ppObj:127},127,!0),e.defineType("try_hash",{ppObj:127},127,!0),e.defineType("hascsize",{ppObj:127},127,!0),e.defineType("hasflag",{klass:127,dest:127,sp1:127,sp2:127},64,!0),e.defineType("array_rank",{destination:127,source:127},127,!0),e.defineType("stfld_o",{locals:127,fieldOffsetBytes:127,targetLocalOffsetBytes:127,sourceLocalOffsetBytes:127},127,!0),e.defineType("notnull",{ptr:127,expected:127,traceIp:127,ip:127},64,!0),e.defineType("stelemr",{o:127,aindex:127,ref:127},127,!0),e.defineType("simd_p_p",{arg0:127,arg1:127},64,!0),e.defineType("simd_p_pp",{arg0:127,arg1:127,arg2:127},64,!0),e.defineType("simd_p_ppp",{arg0:127,arg1:127,arg2:127,arg3:127},64,!0);const t=vc();for(let n=0;n<t.length;n++)t[n]||ut(!1,`trace #${n} missing`),e.defineImportedFunction("i",t[n][0],t[n][1],!0,t[n][2])}(p)),oc=p.options;const u=r+s,d=`${t}:${(n-r).toString(16)}`,f=Ms();let _=0,m=!0,h=!1;const g=dc[a],b=g.isVerbose||i&&ac.findIndex((e=>i.indexOf(e)>=0))>=0;b&&!i&&ut(!1,"Expected methodFullName if trace is instrumented");const y=b?pc++:0;b&&(Fe(`instrumenting: ${i}`),lc[y]=new ic(i)),p.compressImportNames=!b;try{p.appendU32(1836278016),p.appendU32(1),p.generateTypeSection();const t={disp:127,cknull_ptr:127,dest_ptr:127,src_ptr:127,memop_dest:127,memop_src:127,index:127,count:127,math_lhs32:127,math_rhs32:127,math_lhs64:126,math_rhs64:126,temp_f32:125,temp_f64:124};p.options.enableSimd&&(t.v128_zero=123,t.math_lhs128=123,t.math_rhs128=123);let s=!0,i=0;if(p.defineFunction({type:"trace",name:d,export:!0,locals:t},(()=>{switch(p.base=n,p.traceIndex=a,p.frame=e,B(n)){case 672:case 673:case 675:case 674:break;default:throw new Error(`Expected *ip to be a jiterpreter opcode but it was ${B(n)}`)}return p.cfg.initialize(r,c,b?1:0),i=function(e,t,n,r,s,a,i,c){let l=!0,p=!1,u=!1,d=!1,f=0,_=0,m=0;Ga(),a.backBranchTraceLevel=i?2:0;let h=a.cfg.entry(n);for(;n&&n;){if(a.cfg.ip=n,n>=s){Tc(a.traceIndex,0,0,"end-of-body"),i&&Fe(`instrumented trace ${t} exited at end of body @${n.toString(16)}`);break}const g=3840-a.bytesGeneratedSoFar-a.cfg.overheadBytes;if(a.size>=g){Tc(a.traceIndex,0,0,"trace-too-big"),i&&Fe(`instrumented trace ${t} exited because of size limit at @${n.toString(16)} (spaceLeft=${g}b)`);break}let b=B(n);const y=o.mono_jiterp_get_opcode_info(b,2),w=o.mono_jiterp_get_opcode_info(b,3),k=o.mono_jiterp_get_opcode_info(b,1),S=b>=655&&b<=657,v=S?b-655+2:0,U=S?Ba(n,1+v):0;b>=0&&b<689||ut(!1,`invalid opcode ${b}`);const E=S?_a[v][U]:js(b),T=n,x=a.options.noExitBackwardBranches&&Ma(n,r,c),I=a.branchTargets.has(n),A=x||I||l&&c,j=m+_+a.branchTargets.size;let $=!1,L=ea(b);switch(x&&(a.backBranchTraceLevel>1&&Fe(`${t} recording back branch target 0x${n.toString(16)}`),a.backBranchOffsets.push(n)),A&&(u=!1,d=!1,Qa(a,n,x),p=!0,Ga(),m=0),L<-1&&p&&(L=-2===L?2:0),l=!1,271===b||(sc.indexOf(b)>=0?(Ps(a,n,23),b=676):u&&(b=676)),b){case 676:u&&(d||a.appendU8(0),d=!0);break;case 313:case 314:ni(a,Ba(n,1),0,Ba(n,2));break;case 312:ti(a,Ba(n,1)),Ka(a,Ba(n,2),40),a.local("frame"),a.callImport("localloc");break;case 285:Ka(a,Ba(n,1),40),a.i32_const(0),Ka(a,Ba(n,2),40),a.appendU8(252),a.appendU8(11),a.appendU8(0);break;case 286:Ka(a,Ba(n,1),40),qs(a,0,Ba(n,2));break;case 310:{const e=Ba(n,3),t=Ba(n,2),r=Ba(n,1),o=za(a,e);0!==o&&("number"!=typeof o?(Ka(a,e,40),a.local("count",34),a.block(64,4)):(a.i32_const(o),a.local("count",33)),Ka(a,r,40),a.local("dest_ptr",34),a.appendU8(69),Ka(a,t,40),a.local("src_ptr",34),a.appendU8(69),a.appendU8(114),a.block(64,4),Ps(a,n,2),a.endBlock(),"number"==typeof o&&Gs(a,0,0,o,!1,"dest_ptr","src_ptr")||(a.local("dest_ptr"),a.local("src_ptr"),a.local("count"),a.appendU8(252),a.appendU8(10),a.appendU8(0),a.appendU8(0)),"number"!=typeof o&&a.endBlock());break}case 311:{const e=Ba(n,3),t=Ba(n,2);si(a,Ba(n,1),n,!0),Ka(a,t,40),Ka(a,e,40),a.appendU8(252),a.appendU8(11),a.appendU8(0);break}case 143:case 145:case 227:case 229:case 144:case 146:case 129:case 132:case 133:_i(a,n,e,b)?p=!0:n=0;break;case 538:{const e=Ba(n,2),t=Ba(n,1);e!==t?(a.local("pLocals"),si(a,e,n,!0),ei(a,t,54)):si(a,e,n,!1),a.allowNullCheckOptimization&&Ha.set(t,n),$=!0;break}case 636:case 637:{const t=D(e+Ys(4));a.ptr_const(t),a.callImport("entry"),a.block(64,4),Ps(a,n,1),a.endBlock();break}case 674:L=0;break;case 138:break;case 86:{a.local("pLocals");const e=Ba(n,2),r=oi(a,e),o=Ba(n,1);r||Pe(`${t}: Expected local ${e} to have address taken flag`),ti(a,e),ei(a,o,54),Pa.set(o,{type:"ldloca",offset:e}),$=!0;break}case 272:case 300:case 301:case 556:{a.local("pLocals");let t=Da(e,Ba(n,2));300===b&&(t=o.mono_jiterp_imethod_to_ftnptr(t)),a.ptr_const(t),ei(a,Ba(n,1),54);break}case 305:{const t=Da(e,Ba(n,3));Ka(a,Ba(n,1),40),Ka(a,Ba(n,2),40),a.ptr_const(t),a.callImport("value_copy");break}case 306:{const e=Ba(n,3);Ka(a,Ba(n,1),40),Ka(a,Ba(n,2),40),Js(a,e);break}case 307:{const e=Ba(n,3);ti(a,Ba(n,1),e),si(a,Ba(n,2),n,!0),Js(a,e);break}case 308:{const t=Da(e,Ba(n,3));Ka(a,Ba(n,1),40),ti(a,Ba(n,2),0),a.ptr_const(t),a.callImport("value_copy");break}case 309:{const e=Ba(n,3);Ka(a,Ba(n,1),40),ti(a,Ba(n,2),0),Js(a,e);break}case 540:a.local("pLocals"),si(a,Ba(n,2),n,!0),a.appendU8(40),a.appendMemarg(Ys(2),2),ei(a,Ba(n,1),54);break;case 539:{a.block(),Ka(a,Ba(n,3),40),a.local("index",34);let e="cknull_ptr";a.options.zeroPageOptimization&&ra()?(la(8,1),Ka(a,Ba(n,2),40),e="src_ptr",a.local(e,34)):si(a,Ba(n,2),n,!0),a.appendU8(40),a.appendMemarg(Ys(2),2),a.appendU8(72),a.local("index"),a.i32_const(0),a.appendU8(78),a.appendU8(113),a.appendU8(13),a.appendULeb(0),Ps(a,n,11),a.endBlock(),a.local("pLocals"),a.local("index"),a.i32_const(2),a.appendU8(108),a.local(e),a.appendU8(106),a.appendU8(47),a.appendMemarg(Ys(3),1),ei(a,Ba(n,1),54);break}case 342:case 343:{const e=Na(n,4);a.block(),Ka(a,Ba(n,3),40),a.local("index",34);let t="cknull_ptr";342===b?si(a,Ba(n,2),n,!0):(ti(a,Ba(n,2),0),t="src_ptr",a.local(t,34)),a.appendU8(40),a.appendMemarg(Ys(7),2),a.appendU8(73),a.local("index"),a.i32_const(0),a.appendU8(78),a.appendU8(113),a.appendU8(13),a.appendULeb(0),Ps(a,n,18),a.endBlock(),a.local("pLocals"),a.local(t),a.appendU8(40),a.appendMemarg(Ys(8),2),a.local("index"),a.i32_const(e),a.appendU8(108),a.appendU8(106),ei(a,Ba(n,1),54);break}case 662:a.block(),Ka(a,Ba(n,3),40),a.local("count",34),a.i32_const(0),a.appendU8(78),a.appendU8(13),a.appendULeb(0),Ps(a,n,18),a.endBlock(),ti(a,Ba(n,1),16),a.local("dest_ptr",34),Ka(a,Ba(n,2),40),a.appendU8(54),a.appendMemarg(0,0),a.local("dest_ptr"),a.local("count"),a.appendU8(54),a.appendMemarg(4,0);break;case 577:ti(a,Ba(n,1),8),ti(a,Ba(n,2),8),a.callImport("ld_del_ptr");break;case 73:ti(a,Ba(n,1),4),a.ptr_const(Ca(n,2)),a.callImport("ldtsflda");break;case 661:a.block(),ti(a,Ba(n,1),4),ti(a,Ba(n,2),0),a.callImport("gettype"),a.appendU8(13),a.appendULeb(0),Ps(a,n,2),a.endBlock();break;case 658:{const t=Da(e,Ba(n,4));a.ptr_const(t),ti(a,Ba(n,1),4),ti(a,Ba(n,2),0),ti(a,Ba(n,3),0),a.callImport("hasflag");break}case 667:{const e=Ys(1);a.local("pLocals"),si(a,Ba(n,2),n,!0),a.i32_const(e),a.appendU8(106),ei(a,Ba(n,1),54);break}case 659:a.local("pLocals"),ti(a,Ba(n,2),0),a.callImport("hashcode"),ei(a,Ba(n,1),54);break;case 660:a.local("pLocals"),ti(a,Ba(n,2),0),a.callImport("try_hash"),ei(a,Ba(n,1),54);break;case 663:a.local("pLocals"),ti(a,Ba(n,2),0),a.callImport("hascsize"),ei(a,Ba(n,1),54);break;case 668:a.local("pLocals"),Ka(a,Ba(n,2),40),a.local("math_lhs32",34),Ka(a,Ba(n,3),40),a.appendU8(115),a.i32_const(2),a.appendU8(116),a.local("math_rhs32",33),a.local("math_lhs32"),a.i32_const(327685),a.appendU8(106),a.i32_const(10485920),a.appendU8(114),a.i32_const(1703962),a.appendU8(106),a.i32_const(-8388737),a.appendU8(114),a.local("math_rhs32"),a.appendU8(113),a.appendU8(69),ei(a,Ba(n,1),54);break;case 541:case 542:a.block(),ti(a,Ba(n,1),4),ti(a,Ba(n,2),0),a.callImport(541===b?"array_rank":"a_elesize"),a.appendU8(13),a.appendULeb(0),Ps(a,n,2),a.endBlock();break;case 289:case 290:{const t=Da(e,Ba(n,3)),r=o.mono_jiterp_is_special_interface(t),s=289===b,i=Ba(n,1);if(!t){Tc(a.traceIndex,0,0,"null-klass"),n=0;continue}a.block(),a.options.zeroPageOptimization&&ra()?(Ka(a,Ba(n,2),40),a.local("dest_ptr",34),la(8,1)):(a.block(),Ka(a,Ba(n,2),40),a.local("dest_ptr",34),a.appendU8(13),a.appendULeb(0),a.local("pLocals"),a.i32_const(0),ei(a,i,54),a.appendU8(12),a.appendULeb(1),a.endBlock(),a.local("dest_ptr")),r&&a.local("dest_ptr"),a.appendU8(40),a.appendMemarg(Ys(14),0),a.ptr_const(t),a.callImport(r?"imp_iface_s":"imp_iface"),s&&(a.local("dest_ptr"),a.appendU8(69),a.appendU8(114)),a.block(64,4),a.local("pLocals"),a.local("dest_ptr"),ei(a,i,54),a.appendU8(5),s?Ps(a,n,19):(a.local("pLocals"),a.i32_const(0),ei(a,i,54)),a.endBlock(),a.endBlock();break}case 291:case 292:case 287:case 288:{const t=Da(e,Ba(n,3)),r=291===b||292===b,o=287===b||291===b,s=Ba(n,1);if(!t){Tc(a.traceIndex,0,0,"null-klass"),n=0;continue}a.block(),a.options.zeroPageOptimization&&ra()?(Ka(a,Ba(n,2),40),a.local("dest_ptr",34),la(8,1)):(a.block(),Ka(a,Ba(n,2),40),a.local("dest_ptr",34),a.appendU8(13),a.appendULeb(0),a.local("pLocals"),a.i32_const(0),ei(a,s,54),a.appendU8(12),a.appendULeb(1),a.endBlock(),a.local("dest_ptr")),a.appendU8(40),a.appendMemarg(Ys(14),0),a.appendU8(40),a.appendMemarg(Ys(15),0),r&&a.local("src_ptr",34),a.i32_const(t),a.appendU8(70),a.block(64,4),a.local("pLocals"),a.local("dest_ptr"),ei(a,s,54),a.appendU8(5),r?(a.local("src_ptr"),a.ptr_const(t),a.callImport("hasparent"),o&&(a.local("dest_ptr"),a.appendU8(69),a.appendU8(114)),a.block(64,4),a.local("pLocals"),a.local("dest_ptr"),ei(a,s,54),a.appendU8(5),o?Ps(a,n,19):(a.local("pLocals"),a.i32_const(0),ei(a,s,54)),a.endBlock()):(ti(a,Ba(n,1),4),a.local("dest_ptr"),a.ptr_const(t),a.i32_const(b),a.callImport("castv2"),a.appendU8(69),a.block(64,4),Ps(a,n,19),a.endBlock()),a.endBlock(),a.endBlock();break}case 295:case 296:a.ptr_const(Da(e,Ba(n,3))),ti(a,Ba(n,1),4),ti(a,Ba(n,2),0),a.i32_const(296===b?1:0),a.callImport("box");break;case 299:{const t=Da(e,Ba(n,3)),r=Ys(17),o=Ba(n,1),s=D(t+r);if(!t||!s){Tc(a.traceIndex,0,0,"null-klass"),n=0;continue}a.options.zeroPageOptimization&&ra()?(Ka(a,Ba(n,2),40),a.local("dest_ptr",34),la(8,1)):(si(a,Ba(n,2),n,!0),a.local("dest_ptr",34)),a.appendU8(40),a.appendMemarg(Ys(14),0),a.appendU8(40),a.appendMemarg(Ys(15),0),a.local("src_ptr",34),a.appendU8(40),a.appendMemarg(r,0),a.i32_const(s),a.appendU8(70),a.local("src_ptr"),a.appendU8(45),a.appendMemarg(Ys(16),0),a.appendU8(69),a.appendU8(113),a.block(64,4),a.local("pLocals"),a.local("dest_ptr"),a.i32_const(Ys(18)),a.appendU8(106),ei(a,o,54),a.appendU8(5),Ps(a,n,21),a.endBlock();break}case 294:a.block(),ti(a,Ba(n,1),4),Ka(a,Ba(n,2),40),a.callImport("newstr"),a.appendU8(13),a.appendULeb(0),Ps(a,n,17),a.endBlock();break;case 283:a.block(),ti(a,Ba(n,1),4),a.ptr_const(Da(e,Ba(n,2))),a.callImport("newobj_i"),a.appendU8(13),a.appendULeb(0),Ps(a,n,17),a.endBlock();break;case 282:case 284:case 544:case 543:p?(Vs(a,n,j,15),u=!0,L=0):n=0;break;case 546:case 547:case 548:case 549:case 545:p?(Vs(a,n,j,545==b?22:15),u=!0):n=0;break;case 137:case 134:Ps(a,n,16),u=!0;break;case 130:case 131:Ps(a,n,26),u=!0;break;case 136:if(a.callHandlerReturnAddresses.length>0&&a.callHandlerReturnAddresses.length<=3){const t=Fa(e,Ba(n,1));a.local("pLocals"),a.appendU8(40),a.appendMemarg(t,0),a.local("index",33);for(let e=0;e<a.callHandlerReturnAddresses.length;e++){const t=a.callHandlerReturnAddresses[e];a.local("index"),a.ptr_const(t),a.appendU8(70),a.cfg.branch(t,t<n,1)}Ps(a,n,25)}else n=0;break;case 135:case 633:case 634:n=0;break;case 493:case 498:case 494:case 496:case 503:case 495:case 502:case 497:a.block(),ti(a,Ba(n,1),8),ti(a,Ba(n,2),0),a.i32_const(b),a.callImport("conv"),a.appendU8(13),a.appendULeb(0),Ps(a,n,13),a.endBlock();break;case 456:case 457:case 462:case 463:{const e=456===b||462===b,t=462===b||463===b,r=t?0x8000000000000000:2147483648,o=e?"temp_f32":"temp_f64";a.local("pLocals"),Ka(a,Ba(n,2),e?42:43),a.local(o,34),a.appendU8(e?139:153),a.appendU8(e?67:68),e?a.appendF32(r):a.appendF64(r),a.appendU8(e?93:99),a.block(t?126:127,4),a.local(o),a.appendU8(ha[b]),a.appendU8(5),a.appendU8(t?66:65),a.appendBoundaryValue(t?64:32,-1),a.endBlock(),ei(a,Ba(n,1),t?55:54);break}case 529:case 530:{const e=529===b;a.local("pLocals"),Ka(a,Ba(n,2),e?40:41);const t=Na(n,3),r=Na(n,4);e?a.i32_const(t):a.i52_const(t),a.appendU8(e?106:124),e?a.i32_const(r):a.i52_const(r),a.appendU8(e?108:126),ei(a,Ba(n,1),e?54:55);break}case 648:case 649:{const e=649===b;a.local("pLocals"),Ka(a,Ba(n,2),e?41:40),e?a.i52_const(1):a.i32_const(1),a.appendU8(e?132:114),a.appendU8(e?121:103),e&&a.appendU8(167),a.i32_const(e?63:31),a.appendU8(115),ei(a,Ba(n,1),54);break}case 531:case 532:{const e=531===b,t=e?40:41,r=e?54:55;a.local("pLocals"),Ka(a,Ba(n,2),t),Ka(a,Ba(n,3),t),e?a.i32_const(31):a.i52_const(63),a.appendU8(e?113:131),a.appendU8(e?116:134),ei(a,Ba(n,1),r);break}case 590:case 617:{const e=617===b,t=e?42:43,r=e?56:57;a.local("pLocals"),Ka(a,Ba(n,2),t),Ka(a,Ba(n,3),t),Ka(a,Ba(n,4),t),a.callImport(e?"fmaf":"fma"),ei(a,Ba(n,1),r);break}default:b>=3&&b<=12||b>=509&&b<=510?p||a.options.countBailouts?(Ps(a,n,14),u=!0):n=0:b>=13&&b<=21?ai(a,n,b)?$=!0:n=0:b>=74&&b<=85?ii(a,n,b)||(n=0):b>=344&&b<=427?pi(a,n,b)||(n=0):ga[b]?ui(a,n,b)||(n=0):wa[b]?mi(a,n,e,b)?p=!0:n=0:b>=23&&b<=49?ci(a,e,n,b)||(n=0):b>=50&&b<=73?li(a,e,n,b)||(n=0):b>=87&&b<=127?gi(a,n,b)||(n=0):b>=578&&b<=631?hi(a,n,b)||(n=0):b>=315&&b<=341?yi(a,e,n,b)||(n=0):b>=227&&b<=270?a.branchTargets.size>0?(Vs(a,n,j,8),u=!0):n=0:b>=650&&b<=657?(a.containsSimd=!0,Si(a,n,b,E,v,U)?$=!0:n=0):b>=559&&b<=571?(a.containsAtomics=!0,Ti(a,n,b)||(n=0)):0===L||(n=0)}if(n){if(!$){const e=n+2;for(let t=0;t<w;t++)Ja(B(e+2*t))}if(oc.dumpTraces||i){let e=`${n.toString(16)} ${E} `;const t=n+2,r=t+2*w;for(let t=0;t<y;t++)0!==t&&(e+=", "),e+=B(r+2*t);w>0&&(e+=" -> ");for(let n=0;n<w;n++)0!==n&&(e+=", "),e+=B(t+2*n);a.traceBuf.push(e)}L>0&&(p?m++:_++,f+=L),(n+=2*k)<=s&&(h=n)}else i&&Fe(`instrumented trace ${t} aborted for opcode ${E} @${T.toString(16)}`),Tc(a.traceIndex,0,0,b)}for(;a.activeBlocks>0;)a.endBlock();return a.cfg.exitIp=h,a.containsSimd&&(f+=10240),f}(e,d,n,r,u,p,y,c),s=i>=oc.minimumTraceValue,p.cfg.generate()})),p.emitImportsAndFunctions(!1),!s)return g&&"end-of-body"===g.abortReason&&(g.abortReason="trace-too-small"),0;_=Ms();const f=p.getArrayView();if(la(6,f.length),f.length>=4080)return Me(`Jiterpreter generated too much code (${f.length} bytes) for trace ${d}. Please report this issue.`),0;const h=new WebAssembly.Module(f),w=p.getWasmImports(),k=new WebAssembly.Instance(h,w).exports[d];let S;m=!1,l?(zs().set(l,k),S=l):S=Hs(0,k);const v=ca(1);return p.options.enableStats&&v&&v%500==0&&xc(!0),S}catch(e){h=!0,m=!1;let t=p.containsSimd?" (simd)":"";return p.containsAtomics&&(t+=" (atomics)"),Pe(`${i||d}${t} code generation failed: ${e} ${e.stack}`),Xs(),0}finally{const e=Ms();if(_?(la(11,_-f),la(12,e-_)):la(11,e-f),h||!m&&oc.dumpTraces||b){if(h||oc.dumpTraces||b)for(let e=0;e<p.traceBuf.length;e++)Fe(p.traceBuf[e]);Fe(`// ${i||d} generated, blob follows //`);let e="",t=0;try{for(;p.activeBlocks>0;)p.endBlock();p.inSection&&p.endSection()}catch(e){}const n=p.getArrayView();for(let r=0;r<n.length;r++){const o=n[r];o<16&&(e+="0"),e+=o.toString(16),e+=" ",e.length%10==0&&(Fe(`${t}\t${e}`),e="",t=r+1)}Fe(`${t}\t${e}`),Fe("// end blob //")}}}(e,u,n,s,a,r,l,d,c);return f?(la(1,1),p.fnPtr=f,f):oc.estimateHeat?0:1},function(e){const t=Li[e&=-2];if(t){if(Bi||(Bi=pa()),t.hitCount++,t.hitCount===Bi.interpEntryFlushThreshold)Ci();else if(t.hitCount!==Bi.interpEntryHitCount)return;o.mono_jiterp_tlqueue_add(1,e)>=4?Ci():$i>0||"function"==typeof globalThis.setTimeout&&($i=globalThis.setTimeout((()=>{$i=0,Ci()}),10))}},function(e,t,n,r,o,s,a,i){if(n>16)return 0;const c=new Ni(e,t,n,r,o,s,a,i);ji||(ji=zs());const l=ji.get(i),p=(s?a?29:20:a?11:2)+n;return c.result=Hs(p,l),Li[e]=c,c.result},function(e,t,n,r,s){const a=D(n+0),i=qi[a];if(i)return void(i.result>0?o.mono_jiterp_register_jit_call_thunk(n,i.result):(i.queue.push(n),i.queue.length>12&&Qi()));const c=new Ji(e,t,n,r,0!==s);qi[a]=c;const l=o.mono_jiterp_tlqueue_add(0,e);let p=Gi[e];p||(p=Gi[e]=[]),p.push(c),l>=6&&Qi()},function(e,t,n,r,s){const a=Xi(e);try{a(t,n,r,s)}catch(e){const t=Xe.wasmExports.__cpp_exception,n=t instanceof WebAssembly.Tag;if(n&&!(e instanceof WebAssembly.Exception&&e.is(t)))throw e;if(i=s,Xe.HEAPU32[i>>>2]=1,n){const n=e.getArg(t,0);o.mono_jiterp_begin_catch(n),o.mono_jiterp_end_catch()}else{if("number"!=typeof e)throw e;o.mono_jiterp_begin_catch(e),o.mono_jiterp_end_catch()}}var i},Qi,function(e,t,n){delete dc[n],function(e){delete Li[e]}(t),function(e){const t=Gi[e];if(t){for(let e=0;e<t.length;e++)delete qi[t[e].addr];delete Gi[e]}}(e)},function(){ot.enablePerfMeasure&&Ct.push(globalThis.performance.now())},function(e){if(ot.enablePerfMeasure){const t=Ct.pop(),n=tt?{start:t}:{startTime:t};let r=Ot.get(e);r||(r=xe(s.mono_wasm_method_get_name(e)),Ot.set(e,r)),globalThis.performance.measure(r,n)}},function(e,t,n,r,o){const s=xe(n),a=!!r,i=xe(e),c=o,l=xe(t),p=`[MONO] ${s}`;if(Qe.logging&&"function"==typeof Qe.logging.trace)Qe.logging.trace(i,l,p,a,c);else switch(l){case"critical":case"error":{const e=p+"\n"+(new Error).stack;st.exitReason||(st.exitReason=e),console.error(qe(e))}break;case"warning":console.warn(p);break;case"message":default:console.log(p);break;case"info":console.info(p);break;case"debug":console.debug(p)}},function(e){ht=st.config.mainAssemblyName+".dll",gt=e,console.assert(!0,`Adding an entrypoint breakpoint ${ht} at method token  ${gt}`);debugger},function(){},function(e,t){if(!globalThis.crypto||!globalThis.crypto.getRandomValues)return-1;const n=Y(),r=n.subarray(e,e+t),o=(n.buffer,!1),s=o?new Uint8Array(t):r;for(let e=0;e<t;e+=65536){const n=s.subarray(e,e+Math.min(t-e,65536));globalThis.crypto.getRandomValues(n)}return o&&r.set(s),0},function(){console.clear()},Or,function(e){dr();try{return function(e){dr();const t=Bt(),r=On(e);2!==r&&ut(!1,`Signature version ${r} mismatch.`);const o=function(e){e||ut(!1,"Null signatures");const t=P(e+16);if(0===t)return null;const n=P(e+20);return t||ut(!1,"Null name"),Ie(e+t,e+t+n)}(e),s=function(e){e||ut(!1,"Null signatures");const t=P(e+24);return 0===t?null:Ie(e+t,e+t+P(e+28))}(e),a=function(e){return e||ut(!1,"Null signatures"),P(e+8)}(e);st.diagnosticTracing&&De(`Binding [JSImport] ${o} from ${s} module`);const i=function(e,t){e&&"string"==typeof e||ut(!1,"function_name must be string");let n={};const r=e.split(".");t?(n=pr.get(t),n||ut(!1,`ES6 module ${t} was not imported yet, please call JSHost.ImportAsync() first.`)):"INTERNAL"===r[0]?(n=Qe,r.shift()):"globalThis"===r[0]&&(n=globalThis,r.shift());for(let t=0;t<r.length-1;t++){const o=r[t],s=n[o];if(!s)throw new Error(`${o} not found while looking up ${e}`);n=s}const o=n[r[r.length-1]];if("function"!=typeof o)throw new Error(`${e} must be a Function but was ${typeof o}`);return o.bind(n)}(o,s),c=Cn(e),l=new Array(c),p=new Array(c);let u=!1;for(let t=0;t<c;t++){const n=jn(e,t+2),r=$n(n),o=Dt(n,r,t+2);o||ut(!1,"ERR42: argument marshaler must be resolved"),l[t]=o,23===r&&(p[t]=e=>{e&&e.dispose()},u=!0)}const d=jn(e,1),f=$n(d),_=Qr(d,f,1),m=26==f,h=20==f||30==f,g={fn:i,fqn:s+":"+o,args_count:c,arg_marshalers:l,res_converter:_,has_cleanup:u,arg_cleanup:p,is_discard_no_wait:m,is_async:h,isDisposed:!1};let b;b=h||m||u?nr(g):0!=c||_?1!=c||_?1==c&&_?function(e){const t=e.fn,r=e.arg_marshalers[0],o=e.res_converter,s=e.fqn;return e=null,function(a){const i=Bt();try{n&&e.isDisposed;const s=r(a),i=t(s);o(a,i)}catch(e){ho(a,e)}finally{Nt(i,"mono.callCsFunction:",s)}}}(g):2==c&&_?function(e){const t=e.fn,r=e.arg_marshalers[0],o=e.arg_marshalers[1],s=e.res_converter,a=e.fqn;return e=null,function(i){const c=Bt();try{n&&e.isDisposed;const a=r(i),c=o(i),l=t(a,c);s(i,l)}catch(e){ho(i,e)}finally{Nt(c,"mono.callCsFunction:",a)}}}(g):nr(g):function(e){const t=e.fn,r=e.arg_marshalers[0],o=e.fqn;return e=null,function(s){const a=Bt();try{n&&e.isDisposed;const o=r(s);t(o)}catch(e){ho(s,e)}finally{Nt(a,"mono.callCsFunction:",o)}}}(g):function(e){const t=e.fn,r=e.fqn;return e=null,function(o){const s=Bt();try{n&&e.isDisposed,t()}catch(e){ho(o,e)}finally{Nt(s,"mono.callCsFunction:",r)}}}(g);let y=b;y[vn]=g,tr[a]=y,Nt(t,"mono.bindJsFunction:",o)}(e),0}catch(e){return $e(function(e){let t="unknown exception";if(e){t=e.toString();const n=e.stack;n&&(n.startsWith(t)?t=n:t+="\n"+n),t=We(t)}return t}(e))}},function(e,t){!function(e,t){st.assert_runtime_running();const n=Nr(e);n&&"function"==typeof n&&n[Sn]||ut(!1,`Bound function handle expected ${e}`),n(t)}(e,t)},function(e,t){st.assert_runtime_running();const n=tr[e];n||ut(!1,`Imported function handle expected ${e}`),n(t)},function(e){fr((()=>function(e){if(!st.is_runtime_running())return void(st.diagnosticTracing&&De("This promise resolution/rejection can't be propagated to managed code, mono runtime already exited."));const t=In(e,0),r=n;try{st.assert_runtime_running();const n=In(e,1),o=In(e,2),s=In(e,3),a=Dn(o),i=qn(o),c=Nr(i);c||ut(!1,`Cannot find Promise for JSHandle ${i}`),c.resolve_or_reject(a,i,s),r||(Mn(n,1),Mn(t,0))}catch(e){ho(t,e)}}(e)))},function(e){fr((()=>function(e){if(!st.is_runtime_running())return void(st.diagnosticTracing&&De("This promise can't be canceled, mono runtime already exited."));const t=Vr(e);t||ut(!1,`Expected Promise for GCHandle ${e}`),t.cancel()}(e)))},function(e,t,n,r,o,s,a){return"function"==typeof at.mono_wasm_change_case?at.mono_wasm_change_case(e,t,n,r,o,s,a):0},function(e,t,n,r,o,s,a,i){return"function"==typeof at.mono_wasm_compare_string?at.mono_wasm_compare_string(e,t,n,r,o,s,a,i):0},function(e,t,n,r,o,s,a,i){return"function"==typeof at.mono_wasm_starts_with?at.mono_wasm_starts_with(e,t,n,r,o,s,a,i):0},function(e,t,n,r,o,s,a,i){return"function"==typeof at.mono_wasm_ends_with?at.mono_wasm_ends_with(e,t,n,r,o,s,a,i):0},function(e,t,n,r,o,s,a,i,c){return"function"==typeof at.mono_wasm_index_of?at.mono_wasm_index_of(e,t,n,r,o,s,a,i,c):0},function(e,t,n,r,o,s){return"function"==typeof at.mono_wasm_get_calendar_info?at.mono_wasm_get_calendar_info(e,t,n,r,o,s):0},function(e,t,n,r,o){return"function"==typeof at.mono_wasm_get_culture_info?at.mono_wasm_get_culture_info(e,t,n,r,o):0},function(e,t,n){return"function"==typeof at.mono_wasm_get_first_day_of_week?at.mono_wasm_get_first_day_of_week(e,t,n):0},function(e,t,n){return"function"==typeof at.mono_wasm_get_first_week_of_year?at.mono_wasm_get_first_week_of_year(e,t,n):0},function(e,t,n,r,o,s,a){try{const i=Ie(n,n+2*r),c=Dc(i);if(!c&&i)return je(o,o+2*i.length,i),v(a,i.length),0;const l=Dc(Ie(e,e+2*t));if(!c||!l)throw new Error(`Locale or culture name is null or empty. localeName=${c}, cultureName=${l}`);const p=c.split("-");let u,d;try{const e=p.length>1?p.pop():void 0;d=e?new Intl.DisplayNames([l],{type:"region"}).of(e):void 0;const t=p.join("-");u=new Intl.DisplayNames([l],{type:"language"}).of(t)}catch(e){if(!(e instanceof RangeError))throw e;try{u=new Intl.DisplayNames([l],{type:"language"}).of(c)}catch(e){if(e instanceof RangeError&&i)return je(o,o+2*i.length,i),v(a,i.length),0;throw e}}const f={LanguageName:u,RegionName:d},_=Object.values(f).join("##");if(!_)throw new Error(`Locale info for locale=${c} is null or empty.`);if(_.length>s)throw new Error(`Locale info for locale=${c} exceeds length of ${s}.`);return je(o,o+2*_.length,_),v(a,_.length),0}catch(e){return v(a,-1),$e(e.toString())}}];async function Mc(e,t){try{const n=await Pc(e,t);return st.mono_exit(n),n}catch(e){try{st.mono_exit(1,e)}catch(e){}return e&&"number"==typeof e.status?e.status:1}}async function Pc(e,t){null!=e&&""!==e||(e=st.config.mainAssemblyName)||ut(!1,"Null or empty config.mainAssemblyName"),null==t&&(t=ot.config.applicationArguments),null==t&&(t=Ye?(await import(/*! webpackIgnore: true */"process")).argv.slice(2):[]),function(e,t){const n=t.length+1,r=Xe._malloc(4*n);let s=0;Xe.setValue(r+4*s,o.mono_wasm_strdup(e),"i32"),s+=1;for(let e=0;e<t.length;++e)Xe.setValue(r+4*s,o.mono_wasm_strdup(t[e]),"i32"),s+=1;o.mono_wasm_set_main_args(n,r)}(e,t),st.config.mainAssemblyName=e,-1==ot.waitForDebugger&&(Fe("waiting for debugger..."),await new Promise((e=>{const t=setInterval((()=>{1==ot.waitForDebugger&&(clearInterval(t),e())}),100)})));try{return Xe.runtimeKeepalivePush(),await new Promise((e=>globalThis.setTimeout(e,0))),await function(e,t,n){st.assert_runtime_running();const r=Xe.stackSave();try{const r=xn(5),o=In(r,1),s=In(r,2),a=In(r,3),i=In(r,4),c=function(e){const t=Xe.lengthBytesUTF8(e)+1,n=Xe._malloc(t),r=Y().subarray(n,n+t);return Xe.stringToUTF8Array(e,r,0,t),r[t-1]=0,n}(e);io(s,c),wo(a,t&&!t.length?void 0:t,15),Zr(i,n);let l=tn(o,0,Ht);return hn(ot.managedThreadTID,mn.CallEntrypoint,r),l=nn(r,Ht,l),null==l&&(l=Promise.resolve(0)),l[Br]=!0,l}finally{Xe.stackRestore(r)}}(e,t,1==ot.waitForDebugger)}finally{Xe.runtimeKeepalivePop()}}function Vc(e){ot.runtimeReady&&(ot.runtimeReady=!1,o.mono_wasm_exit(e))}function zc(e){if(st.exitReason=e,ot.runtimeReady){ot.runtimeReady=!1;const t=qe(e);Xe.abort(t)}throw e}async function Hc(e){e.out||(e.out=console.log.bind(console)),e.err||(e.err=console.error.bind(console)),e.print||(e.print=e.out),e.printErr||(e.printErr=e.err),st.out=e.print,st.err=e.printErr,await async function(){var e;if(Ye){if(globalThis.performance===Uo){const{performance:e}=Qe.require("perf_hooks");globalThis.performance=e}if(Qe.process=await import(/*! webpackIgnore: true */"process"),globalThis.crypto||(globalThis.crypto={}),!globalThis.crypto.getRandomValues){let e;try{e=Qe.require("node:crypto")}catch(e){}e?e.webcrypto?globalThis.crypto=e.webcrypto:e.randomBytes&&(globalThis.crypto.getRandomValues=t=>{t&&t.set(e.randomBytes(t.length))}):globalThis.crypto.getRandomValues=()=>{throw new Error("Using node without crypto support. To enable current operation, either provide polyfill for 'globalThis.crypto.getRandomValues' or enable 'node:crypto' module.")}}}ot.subtle=null===(e=globalThis.crypto)||void 0===e?void 0:e.subtle}()}function Wc(e){const t=Bt();e.locateFile||(e.locateFile=e.__locateFile=e=>st.scriptDirectory+e),e.mainScriptUrlOrBlob=st.scriptUrl;const a=e.instantiateWasm,c=e.preInit?"function"==typeof e.preInit?[e.preInit]:e.preInit:[],l=e.preRun?"function"==typeof e.preRun?[e.preRun]:e.preRun:[],p=e.postRun?"function"==typeof e.postRun?[e.postRun]:e.postRun:[],u=e.onRuntimeInitialized?e.onRuntimeInitialized:()=>{};e.instantiateWasm=(e,t)=>function(e,t,n){const r=Bt();if(n){const o=n(e,((e,n)=>{Nt(r,"mono.instantiateWasm"),ot.afterInstantiateWasm.promise_control.resolve(),t(e,n)}));return o}return async function(e,t){try{await st.afterConfigLoaded,st.diagnosticTracing&&De("instantiate_wasm_module"),await ot.beforePreInit.promise,Xe.addRunDependency("instantiate_wasm_module"),await async function(){ot.featureWasmSimd=await st.simd(),ot.featureWasmEh=await st.exceptions(),ot.emscriptenBuildOptions.wasmEnableSIMD&&(ot.featureWasmSimd||ut(!1,"This browser/engine doesn't support WASM SIMD. Please use a modern version. See also https://aka.ms/dotnet-wasm-features")),ot.emscriptenBuildOptions.wasmEnableEH&&(ot.featureWasmEh||ut(!1,"This browser/engine doesn't support WASM exception handling. Please use a modern version. See also https://aka.ms/dotnet-wasm-features"))}(),function(e){const t=e.env||e.a;if(!t)return void Me("WARNING: Neither imports.env or imports.a were present when instantiating the wasm module. This likely indicates an emscripten configuration issue.");const n=new Array(Fc.length);for(const e in t){const r=t[e];if("function"==typeof r&&-1!==r.toString().indexOf("runtime_idx"))try{const{runtime_idx:t}=r();if(void 0!==n[t])throw new Error(`Duplicate runtime_idx ${t}`);n[t]=e}catch(e){}}for(const[e,r]of Fc.entries()){const o=n[e];if(void 0!==o){if("function"!=typeof t[o])throw new Error(`Expected ${o} to be a function`);t[o]=r}}}(e);const n=await st.wasmCompilePromise.promise;t(await WebAssembly.instantiate(n,e),n),st.diagnosticTracing&&De("instantiate_wasm_module done"),ot.afterInstantiateWasm.promise_control.resolve()}catch(e){throw Pe("instantiate_wasm_module() failed",e),st.mono_exit(1,e),e}Xe.removeRunDependency("instantiate_wasm_module")}(e,t),[]}(e,t,a),e.preInit=[()=>function(e){Xe.addRunDependency("mono_pre_init");const t=Bt();try{Xe.addRunDependency("mono_wasm_pre_init_essential"),st.diagnosticTracing&&De("mono_wasm_pre_init_essential"),st.gitHash!==ot.gitHash&&Me(`The version of dotnet.runtime.js ${ot.gitHash} is different from the version of dotnet.js ${st.gitHash}!`),st.gitHash!==ot.emscriptenBuildOptions.gitHash&&Me(`The version of dotnet.native.js ${ot.emscriptenBuildOptions.gitHash}  is different from the version of dotnet.js ${st.gitHash}!`),n!==ot.emscriptenBuildOptions.wasmEnableThreads&&Me(`The threads of dotnet.native.js ${ot.emscriptenBuildOptions.wasmEnableThreads} is different from the version of dotnet.runtime.js ${n}!`),function(){const e=[...r];for(const t of e){const e=o,[n,r,s,a,c]=t,l="function"==typeof n;if(!0===n||l)e[r]=function(...t){!l||!n()||ut(!1,`cwrap ${r} should not be called when binding was skipped`);const o=i(r,s,a,c);return e[r]=o,o(...t)};else{const t=i(r,s,a,c);e[r]=t}}}(),a=Qe,Object.assign(a,{mono_wasm_exit:o.mono_wasm_exit,mono_wasm_profiler_init_aot:s.mono_wasm_profiler_init_aot,mono_wasm_profiler_init_browser:s.mono_wasm_profiler_init_browser,mono_wasm_exec_regression:o.mono_wasm_exec_regression,mono_wasm_print_thread_dump:void 0}),Xe.removeRunDependency("mono_wasm_pre_init_essential"),st.diagnosticTracing&&De("preInit"),ot.beforePreInit.promise_control.resolve(),e.forEach((e=>e()))}catch(e){throw Pe("user preInint() failed",e),st.mono_exit(1,e),e}var a;(async()=>{try{await async function(){st.diagnosticTracing&&De("mono_wasm_pre_init_essential_async"),Xe.addRunDependency("mono_wasm_pre_init_essential_async"),Xe.removeRunDependency("mono_wasm_pre_init_essential_async")}(),Nt(t,"mono.preInit")}catch(e){throw st.mono_exit(1,e),e}ot.afterPreInit.promise_control.resolve(),Xe.removeRunDependency("mono_pre_init")})()}(c)],e.preRun=[()=>async function(e){Xe.addRunDependency("mono_pre_run_async");try{await ot.afterInstantiateWasm.promise,await ot.afterPreInit.promise,st.diagnosticTracing&&De("preRunAsync");const t=Bt();e.map((e=>e())),Nt(t,"mono.preRun")}catch(e){throw Pe("preRunAsync() failed",e),st.mono_exit(1,e),e}ot.afterPreRun.promise_control.resolve(),Xe.removeRunDependency("mono_pre_run_async")}(l)],e.onRuntimeInitialized=()=>async function(e){try{await ot.afterPreRun.promise,st.diagnosticTracing&&De("onRuntimeInitialized"),ot.nativeExit=Vc,ot.nativeAbort=zc;const t=Bt();if(ot.beforeOnRuntimeInitialized.promise_control.resolve(),await ot.coreAssetsInMemory.promise,ot.config.virtualWorkingDirectory){const e=Xe.FS,t=ot.config.virtualWorkingDirectory;try{const n=e.stat(t);n?n&&e.isDir(n.mode)||ut(!1,`FS.chdir: ${t} is not a directory`):Xe.FS_createPath("/",t,!0,!0)}catch(e){Xe.FS_createPath("/",t,!0,!0)}e.chdir(t)}ot.config.interpreterPgo&&setTimeout(Gc,1e3*(ot.config.interpreterPgoSaveDelay||15)),Xe.runtimeKeepalivePush(),n||await async function(){try{const t=Bt();st.diagnosticTracing&&De("Initializing mono runtime");for(const e in ot.config.environmentVariables){const t=ot.config.environmentVariables[e];if("string"!=typeof t)throw new Error(`Expected environment variable '${e}' to be a string but it was ${typeof t}: '${t}'`);qc(e,t)}ot.config.runtimeOptions&&function(e){if(!Array.isArray(e))throw new Error("Expected runtimeOptions to be an array of strings");const t=Xe._malloc(4*e.length);let n=0;for(let r=0;r<e.length;++r){const s=e[r];if("string"!=typeof s)throw new Error("Expected runtimeOptions to be an array of strings");Xe.setValue(t+4*n,o.mono_wasm_strdup(s),"i32"),n+=1}o.mono_wasm_parse_runtime_options(e.length,t)}(ot.config.runtimeOptions),ot.config.aotProfilerOptions&&function(e){ot.emscriptenBuildOptions.enableAotProfiler||ut(!1,"AOT profiler is not enabled, please use <WasmProfilers>aot;</WasmProfilers> in your project file."),null==e&&(e={}),"writeAt"in e||(e.writeAt="System.Runtime.InteropServices.JavaScript.JavaScriptExports::StopProfile"),"sendTo"in e||(e.sendTo="Interop/Runtime::DumpAotProfileData");const t="aot:write-at-method="+e.writeAt+",send-to-method="+e.sendTo;s.mono_wasm_profiler_init_aot(t)}(ot.config.aotProfilerOptions),ot.config.browserProfilerOptions&&(ot.config.browserProfilerOptions,ot.emscriptenBuildOptions.enableBrowserProfiler||ut(!1,"Browser profiler is not enabled, please use <WasmProfilers>browser;</WasmProfilers> in your project file."),s.mono_wasm_profiler_init_browser("browser:")),ot.config.logProfilerOptions&&(e=ot.config.logProfilerOptions,ot.emscriptenBuildOptions.enableLogProfiler||ut(!1,"Log profiler is not enabled, please use <WasmProfilers>log;</WasmProfilers> in your project file."),e.takeHeapshot||ut(!1,"Log profiler is not enabled, the takeHeapshot method must be defined in LogProfilerOptions.takeHeapshot"),s.mono_wasm_profiler_init_log((e.configuration||"log:alloc,output=output.mlpd")+`,take-heapshot-method=${e.takeHeapshot}`)),function(){st.diagnosticTracing&&De("mono_wasm_load_runtime");try{const e=Bt();let t=ot.config.debugLevel;null==t&&(t=0,ot.config.debugLevel&&(t=0+t)),o.mono_wasm_load_runtime(t),Nt(e,"mono.loadRuntime")}catch(e){throw Pe("mono_wasm_load_runtime () failed",e),st.mono_exit(1,e),e}}(),function(){if(da)return;da=!0;const e=pa(),t=e.tableSize,n=ot.emscriptenBuildOptions.runAOTCompilation?e.tableSize:1,r=ot.emscriptenBuildOptions.runAOTCompilation?e.aotTableSize:1,s=t+n+36*r+1,a=zs();let i=a.length;const c=performance.now();a.grow(s);const l=performance.now();e.enableStats&&Fe(`Allocated ${s} function table entries for jiterpreter, bringing total table size to ${a.length}`),i=ua(0,i,t,Zs("mono_jiterp_placeholder_trace")),i=ua(1,i,n,Zs("mono_jiterp_placeholder_jit_call"));for(let e=2;e<=37;e++)i=ua(e,i,r,a.get(o.mono_jiterp_get_interp_entry_func(e)));const p=performance.now();e.enableStats&&Fe(`Growing wasm function table took ${l-c}. Filling table took ${p-l}.`)}(),function(){if(!ot.mono_wasm_bindings_is_ready){st.diagnosticTracing&&De("bindings_init"),ot.mono_wasm_bindings_is_ready=!0;try{const e=Bt();he||("undefined"!=typeof TextDecoder&&(be=new TextDecoder("utf-16le"),ye=new TextDecoder("utf-8",{fatal:!1}),we=new TextDecoder("utf-8"),ke=new TextEncoder),he=Xe._malloc(12)),Se||(Se=function(e){let t;if(le.length>0)t=le.pop();else{const e=function(){if(null==ae||!ie){ae=ue(se,"js roots"),ie=new Int32Array(se),ce=se;for(let e=0;e<se;e++)ie[e]=se-e-1}if(ce<1)throw new Error("Out of scratch root space");const e=ie[ce-1];return ce--,e}();t=new de(ae,e)}if(void 0!==e){if("number"!=typeof e)throw new Error("value must be an address in the managed heap");t.set(e)}else t.set(0);return t}()),function(){const e="System.Runtime.InteropServices.JavaScript";if(ot.runtime_interop_module=o.mono_wasm_assembly_load(e),!ot.runtime_interop_module)throw"Can't find bindings module assembly: "+e;if(ot.runtime_interop_namespace=e,ot.runtime_interop_exports_classname="JavaScriptExports",ot.runtime_interop_exports_class=o.mono_wasm_assembly_find_class(ot.runtime_interop_module,ot.runtime_interop_namespace,ot.runtime_interop_exports_classname),!ot.runtime_interop_exports_class)throw"Can't find "+ot.runtime_interop_namespace+"."+ot.runtime_interop_exports_classname+" class";mn.InstallMainSynchronizationContext=void 0,mn.CallEntrypoint=bn("CallEntrypoint"),mn.BindAssemblyExports=bn("BindAssemblyExports"),mn.ReleaseJSOwnedObjectByGCHandle=bn("ReleaseJSOwnedObjectByGCHandle"),mn.CompleteTask=bn("CompleteTask"),mn.CallDelegate=bn("CallDelegate"),mn.GetManagedStackTrace=bn("GetManagedStackTrace"),mn.LoadSatelliteAssembly=bn("LoadSatelliteAssembly"),mn.LoadLazyAssembly=bn("LoadLazyAssembly")}(),0==yn.size&&(yn.set(21,pn),yn.set(23,dn),yn.set(22,fn),yn.set(3,Mt),yn.set(4,Pt),yn.set(5,Vt),yn.set(6,zt),yn.set(7,Ht),yn.set(8,Wt),yn.set(9,qt),yn.set(11,Gt),yn.set(12,Xt),yn.set(10,Jt),yn.set(15,sn),yn.set(16,an),yn.set(27,an),yn.set(13,cn),yn.set(14,ln),yn.set(17,Yt),yn.set(18,Yt),yn.set(20,en),yn.set(29,en),yn.set(28,en),yn.set(30,tn),yn.set(24,Zt),yn.set(25,Zt),yn.set(0,Qt),yn.set(1,Qt),yn.set(2,Qt),yn.set(26,Qt)),0==wn.size&&(wn.set(21,yo),wn.set(23,ko),wn.set(22,So),wn.set(3,Zr),wn.set(4,Kr),wn.set(5,eo),wn.set(6,to),wn.set(7,no),wn.set(8,ro),wn.set(9,oo),wn.set(10,so),wn.set(11,ao),wn.set(12,io),wn.set(17,co),wn.set(18,lo),wn.set(15,po),wn.set(16,ho),wn.set(27,ho),wn.set(13,go),wn.set(14,bo),wn.set(20,mo),wn.set(28,mo),wn.set(29,mo),wn.set(24,_o),wn.set(25,_o),wn.set(0,fo),wn.set(2,fo),wn.set(1,fo),wn.set(26,fo)),ot._i52_error_scratch_buffer=Xe._malloc(4),Nt(e,"mono.bindingsInit")}catch(e){throw Pe("Error in bindings_init",e),e}}}(),ot.runtimeReady=!0,ot.afterMonoStarted.promise_control.resolve(),ot.config.interpreterPgo&&await jc(),Nt(t,"mono.startRuntime")}catch(e){throw Pe("start_runtime() failed",e),st.mono_exit(1,e),e}var e}(),await async function(){await ot.allAssetsInMemory.promise,ot.config.assets&&(st.actual_downloaded_assets_count!=st.expected_downloaded_assets_count&&ut(!1,`Expected ${st.expected_downloaded_assets_count} assets to be downloaded, but only finished ${st.actual_downloaded_assets_count}`),st.actual_instantiated_assets_count!=st.expected_instantiated_assets_count&&ut(!1,`Expected ${st.expected_instantiated_assets_count} assets to be in memory, but only instantiated ${st.actual_instantiated_assets_count}`),st._loaded_files.forEach((e=>st.loadedFiles.push(e.url))),st.diagnosticTracing&&De("all assets are loaded in wasm memory"))}(),Xc.registerRuntime(rt),0===st.config.debugLevel||ot.mono_wasm_runtime_is_ready||function mono_wasm_runtime_ready(){if(Qe.mono_wasm_runtime_is_ready=ot.mono_wasm_runtime_is_ready=!0,yt=0,bt={},wt=-1,globalThis.dotnetDebugger)debugger}(),0!==st.config.debugLevel&&st.config.cacheBootResources&&st.logDownloadStatsToConsole(),setTimeout((()=>{st.purgeUnusedCacheEntriesAsync()}),st.config.cachedResourcesPurgeDelay);try{e()}catch(e){throw Pe("user callback onRuntimeInitialized() failed",e),e}await async function(){st.diagnosticTracing&&De("mono_wasm_after_user_runtime_initialized");try{if(Xe.onDotnetReady)try{await Xe.onDotnetReady()}catch(e){throw Pe("onDotnetReady () failed",e),e}}catch(e){throw Pe("mono_wasm_after_user_runtime_initialized () failed",e),e}}(),Nt(t,"mono.onRuntimeInitialized")}catch(e){throw Xe.runtimeKeepalivePop(),Pe("onRuntimeInitializedAsync() failed",e),st.mono_exit(1,e),e}ot.afterOnRuntimeInitialized.promise_control.resolve()}(u),e.postRun=[()=>async function(e){try{await ot.afterOnRuntimeInitialized.promise,st.diagnosticTracing&&De("postRunAsync");const t=Bt();Xe.FS_createPath("/","usr",!0,!0),Xe.FS_createPath("/","usr/share",!0,!0),e.map((e=>e())),Nt(t,"mono.postRun")}catch(e){throw Pe("postRunAsync() failed",e),st.mono_exit(1,e),e}ot.afterPostRun.promise_control.resolve()}(p)],e.ready.then((async()=>{await ot.afterPostRun.promise,Nt(t,"mono.emscriptenStartup"),ot.dotnetReady.promise_control.resolve(rt)})).catch((e=>{ot.dotnetReady.promise_control.reject(e)})),e.ready=ot.dotnetReady.promise}function qc(e,t){o.mono_wasm_setenv(e,t)}async function Gc(){void 0!==st.exitCode&&0!==st.exitCode||await Ac()}async function Jc(e){}let Xc;function Qc(r){const o=Xe,s=r,a=globalThis;Object.assign(s.internal,{mono_wasm_exit:e=>{Xe.err("early exit "+e)},forceDisposeProxies:Hr,mono_wasm_dump_threads:void 0,logging:void 0,mono_wasm_stringify_as_error_with_stack:qe,mono_wasm_get_loaded_files:Is,mono_wasm_send_dbg_command_with_parms:St,mono_wasm_send_dbg_command:vt,mono_wasm_get_dbg_command_info:Ut,mono_wasm_get_details:$t,mono_wasm_release_object:Rt,mono_wasm_call_function_on:jt,mono_wasm_debugger_resume:Et,mono_wasm_detach_debugger:Tt,mono_wasm_raise_debug_event:It,mono_wasm_change_debugger_log_level:xt,mono_wasm_debugger_attached:At,mono_wasm_runtime_is_ready:ot.mono_wasm_runtime_is_ready,mono_wasm_get_func_id_to_name_mappings:Je,get_property:sr,set_property:or,has_property:ar,get_typeof_property:ir,get_global_this:cr,get_dotnet_instance:()=>rt,dynamic_import:ur,mono_wasm_bind_cs_function:hr,ws_wasm_create:hs,ws_wasm_open:gs,ws_wasm_send:bs,ws_wasm_receive:ys,ws_wasm_close:ws,ws_wasm_abort:ks,ws_get_state:ms,http_wasm_supports_streaming_request:Ao,http_wasm_supports_streaming_response:jo,http_wasm_create_controller:$o,http_wasm_get_response_type:Fo,http_wasm_get_response_status:Mo,http_wasm_abort:Ro,http_wasm_transform_stream_write:Bo,http_wasm_transform_stream_close:No,http_wasm_fetch:Do,http_wasm_fetch_stream:Co,http_wasm_fetch_bytes:Oo,http_wasm_get_response_header_names:Po,http_wasm_get_response_header_values:Vo,http_wasm_get_response_bytes:Ho,http_wasm_get_response_length:zo,http_wasm_get_streamed_response_bytes:Wo,jiterpreter_dump_stats:xc,jiterpreter_apply_options:ia,jiterpreter_get_options:pa,interp_pgo_load_data:jc,interp_pgo_save_data:Ac,mono_wasm_gc_lock:re,mono_wasm_gc_unlock:oe,monoObjectAsBoolOrNullUnsafe:Nc,monoStringToStringUnsafe:Ce,loadLazyAssembly:Rc,loadSatelliteAssemblies:Bc});const i={stringify_as_error_with_stack:qe,instantiate_symbols_asset:Ts,instantiate_asset:Es,jiterpreter_dump_stats:xc,forceDisposeProxies:Hr,instantiate_segmentation_rules_asset:xs};"hybrid"===st.config.globalizationMode&&(i.stringToUTF16=je,i.stringToUTF16Ptr=$e,i.utf16ToString=Ie,i.utf16ToStringLoop=Ae,i.localHeapViewU16=Z,i.setU16_local=y,i.setI32=v),Object.assign(ot,i);const c={runMain:Pc,runMainAndExit:Mc,exit:st.mono_exit,setEnvironmentVariable:qc,getAssemblyExports:yr,setModuleImports:rr,getConfig:()=>ot.config,invokeLibraryInitializers:st.invokeLibraryInitializers,setHeapB32:m,setHeapB8:h,setHeapU8:g,setHeapU16:b,setHeapU32:w,setHeapI8:k,setHeapI16:S,setHeapI32:v,setHeapI52:E,setHeapU52:T,setHeapI64Big:x,setHeapF32:I,setHeapF64:A,getHeapB32:$,getHeapB8:L,getHeapU8:R,getHeapU16:B,getHeapU32:N,getHeapI8:F,getHeapI16:M,getHeapI32:P,getHeapI52:V,getHeapU52:z,getHeapI64Big:H,getHeapF32:W,getHeapF64:q,localHeapViewU8:Y,localHeapViewU16:Z,localHeapViewU32:K,localHeapViewI8:G,localHeapViewI16:J,localHeapViewI32:X,localHeapViewI64Big:Q,localHeapViewF32:ee,localHeapViewF64:te};return Object.assign(rt,{INTERNAL:s.internal,Module:o,runtimeBuildInfo:{productVersion:e,gitHash:ot.gitHash,buildConfiguration:t,wasmEnableThreads:n,wasmEnableSIMD:!0,wasmEnableExceptionHandling:!0},...c}),a.getDotnetRuntime?Xc=a.getDotnetRuntime.__list:(a.getDotnetRuntime=e=>a.getDotnetRuntime.__list.getRuntime(e),a.getDotnetRuntime.__list=Xc=new Yc),rt}class Yc{constructor(){this.list={}}registerRuntime(e){return void 0===e.runtimeId&&(e.runtimeId=Object.keys(this.list).length),this.list[e.runtimeId]=mr(e),st.config.runtimeId=e.runtimeId,e.runtimeId}getRuntime(e){const t=this.list[e];return t?t.deref():void 0}}export{Wc as configureEmscriptenStartup,Hc as configureRuntimeStartup,Jc as configureWorkerStartup,Qc as initializeExports,Eo as initializeReplacements,ct as passEmscriptenInternals,Xc as runtimeList,lt as setRuntimeGlobals};
//# sourceMappingURL=dotnet.runtime.js.map
