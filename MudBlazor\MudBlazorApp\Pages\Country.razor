@page "/country"
@inherits CountryBase
@using MudBlazor

<MudPaper Class="pa-4" Style="max-width: 33%;">
    @if (IsLoading)
    {
        <MudProgressLinear Color="Color.Primary" Indeterminate="true" Class="my-7" />
        <MudText Align="Align.Center">Loading countries...</MudText>
    }
    else
    {
        @if (!string.IsNullOrEmpty(ErrorMessage))
        {
            <MudAlert Severity="Severity.Warning" Class="mb-3">@ErrorMessage</MudAlert>
        }

        <MudStack Row AlignItems="AlignItems.Center" Justify="Justify.FlexStart" Class="mb-3" Spacing="2">
            <MudSelect T="string" Label="Sort by" @bind-Value="SortOption" Dense="true" Style="width: 200px;">
                <MudSelectItem Value="@("custom")">Custom</MudSelectItem>
                <MudSelectItem Value="@("verified_desc")">Verified DESC</MudSelectItem>
                <MudSelectItem Value="@("name_asc")">Name ASC</MudSelectItem>
                <MudSelectItem Value="@("last_modified_desc")">Last Modified DESC</MudSelectItem>
            </MudSelect>
            <MudButton Variant="Variant.Filled" Color="Color.Primary" OnClick="@AddNew">Add New</MudButton>
            <MudText Typo="Typo.body2">Results: @Countries.Count</MudText>
        </MudStack>

        <MudPaper Elevation="0">
            <MudTable Items="@Countries" Hover="true" Dense="true" Elevation="0" Loading="@IsLoading">
                <HeaderContent>
                    <MudTh Style="background-color:black; color:white; width: 50px;"></MudTh>
                    <MudTh Style="background-color:black; color:white;">ID</MudTh>
                    <MudTh Style="background-color:black; color:white;">Name</MudTh>
                    <MudTh Style="background-color:black; color:white;">Verified?</MudTh>
                    <MudTh Style="background-color:black; color:white;"></MudTh>
                </HeaderContent>
                <RowTemplate>
                    <MudTd>
                        <MudIconButton Icon="@Icons.Material.Filled.Edit" Size="Size.Small" OnClick="@(() => Edit(context))" />
                    </MudTd>
                    <MudTd DataLabel="ID">@context.Id</MudTd>
                    <MudTd DataLabel="Name">@context.Name</MudTd>
                    <MudTd DataLabel="Verified">@((context.Verified) ? "Yes" : "No")</MudTd>
                    <MudTd>
                        <MudButton Variant="Variant.Text" Size="Size.Small" OnClick="@(() => ViewDetails(context))">View Details</MudButton>
                    </MudTd>
                </RowTemplate>
                <NoRecordsContent>
                    <MudText>No countries found</MudText>
                </NoRecordsContent>
            </MudTable>
        </MudPaper>
    }
</MudPaper>
