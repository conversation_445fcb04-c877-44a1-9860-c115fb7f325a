@page "/country"
@page "/country/{id:int}"
@inherits CountryBase
@using MudBlazor

@* Add Country Dialog *@
@if (dialogVisible)
{
    <MudDialog Open="dialogVisible" DisableBackdropClick="true">
        <TitleContent>
            <MudText Typo="Typo.h6">Add/Edit Country</MudText>
        </TitleContent>
        <DialogContent>
            <MudTextField @bind-Value="newCountryName" Label="Enter Country Name" Variant="Variant.Outlined" />
        </DialogContent>
        <DialogActions>
            <MudButton Variant="Variant.Filled" Color="Color.Default" OnClick="CancelAddCountry">Cancel</MudButton>
            <MudButton Variant="Variant.Filled" Color="Color.Primary" OnClick="SaveCountryAsync">OK</MudButton>
        </DialogActions>
    </MudDialog>
}

<MudPaper Class="pa-4" Style="max-width: 33%;">
    @if (IsLoading)
    {
        <MudProgressLinear Color="Color.Primary" Indeterminate="true" Class="my-7" />
        <MudText Align="Align.Center">Loading countries...</MudText>
    }
    else if (showingDetails && selectedCountry != null)
    {
        <!-- Details View -->
        <MudCard>
            <MudCardHeader>
                <CardHeaderContent>
                    <MudText Typo="Typo.h5">Country Details</MudText>
                </CardHeaderContent>
            </MudCardHeader>
            <MudCardContent>
                @if (isLoadingDetails)
                {
                    <MudProgressLinear Color="Color.Primary" Indeterminate="true" Class="my-7" />
                    <MudText Align="Align.Center">Loading details...</MudText>
                }
                else 
                {
                    <MudTable Items="new[] { selectedCountry }" Hover="true" Dense="true" Elevation="0">
                        <HeaderContent>
                            <MudTh Style="background-color:black; color:white;">ID</MudTh>
                            <MudTh Style="background-color:black; color:white;">Name</MudTh>
                            <MudTh Style="background-color:black; color:white;">Verified</MudTh>
                            <MudTh Style="background-color:black; color:white;">Mode</MudTh>
                            <MudTh Style="background-color:black; color:white;">Created On</MudTh>
                            <MudTh Style="background-color:black; color:white;">Last Modified</MudTh>
                        </HeaderContent>
                        <RowTemplate>
                            <MudTd DataLabel="ID">@selectedCountry.Id</MudTd>
                            <MudTd DataLabel="Name">@selectedCountry.Name</MudTd>
                            <MudTd DataLabel="Verified">@(selectedCountry.Verified ? "Yes" : "No")</MudTd>
                            <MudTd DataLabel="Mode">@selectedCountry.Mode</MudTd>
                            <MudTd DataLabel="Created On">@selectedCountry.CreatedOn.ToShortDateString()</MudTd>
                            <MudTd DataLabel="Last Modified">@selectedCountry.LastModified.ToShortDateString()</MudTd>
                        </RowTemplate>
                    </MudTable>
                }
            </MudCardContent>
            <MudCardActions>
                <MudButton Variant="Variant.Filled" Color="Color.Primary" OnClick="BackToList">Back to List</MudButton>
            </MudCardActions>
        </MudCard>
    }
    else
    {
        <!-- List View -->
        @if (!string.IsNullOrEmpty(ErrorMessage))
        {
            <MudAlert Severity="Severity.Warning" Class="mb-3">@ErrorMessage</MudAlert>
        }

        <MudStack Row AlignItems="AlignItems.Center" Justify="Justify.FlexStart" Class="mb-3" Spacing="2">
            <MudSelect T="string" Label="Sort by" @bind-Value="SortOption" Dense="true" Style="width: 200px;">
                <MudSelectItem Value="@("custom")">Custom</MudSelectItem>
                <MudSelectItem Value="@("verified_desc")">Verified DESC</MudSelectItem>
                <MudSelectItem Value="@("name_asc")">Name ASC</MudSelectItem>
                <MudSelectItem Value="@("last_modified_desc")">Last Modified DESC</MudSelectItem>
            </MudSelect>
            <MudButton Variant="Variant.Filled" Color="Color.Primary" OnClick="@AddNew">Add New</MudButton>
            <MudText Typo="Typo.body2">Results: @Countries.Count</MudText>
        </MudStack>

        <MudPaper Elevation="0">
            <MudTable Items="@Countries" Hover="true" Dense="true" Elevation="0" Loading="@IsLoading">
                <HeaderContent>
                    <MudTh Style="background-color:black; color:white; width: 50px;"></MudTh>
                    <MudTh Style="background-color:black; color:white;">ID</MudTh>
                    <MudTh Style="background-color:black; color:white;">Name</MudTh>
                    <MudTh Style="background-color:black; color:white;">Verified?</MudTh>
                    <MudTh Style="background-color:black; color:white;"></MudTh>
                </HeaderContent>
                <RowTemplate>
                    <MudTd>
                        <MudIconButton Icon="@Icons.Material.Filled.Edit" Size="Size.Small" OnClick="@(() => Edit(context))" />
                    </MudTd>
                    <MudTd DataLabel="ID">@context.Id</MudTd>
                    <MudTd DataLabel="Name">@context.Name</MudTd>
                    <MudTd DataLabel="Verified">@((context.Verified) ? "Yes" : "No")</MudTd>
                    <MudTd>
                        <MudButton Variant="Variant.Text" Size="Size.Small" OnClick="@(() => ViewDetails(context))">View Details</MudButton>
                    </MudTd>
                </RowTemplate>
                <NoRecordsContent>
                    <MudText>No countries found</MudText>
                </NoRecordsContent>
            </MudTable>
        </MudPaper>
    }
</MudPaper>
