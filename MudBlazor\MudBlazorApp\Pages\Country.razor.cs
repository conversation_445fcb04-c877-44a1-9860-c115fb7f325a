using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Web;
using MudBlazor;
using MudBlazorApp.Models;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Json;
using System.Threading.Tasks;
using System.Text;
using System.Text.Json;
using System.Net.Http.Headers;

namespace MudBlazorApp.Pages
{
    public partial class Country : CountryBase
    {
     
    }
    public class CountryBase : ComponentBase
    {
        protected string searchString = "";
    private bool sortNameByLength;
    protected int elementsCount = 0;
    
    [Inject]
    private HttpClient Http { get; set; } = default!;
    
    [Inject]
    private IDialogService DialogService { get; set; } = default!;
    
    protected CountryModel? selectedCountry = null;
    protected bool isLoadingDetails = false;
    protected bool showingDetails = false;
    protected string newCountryName = string.Empty;
    protected bool dialogVisible = false;
    
    [Parameter]
    public int? Id { get; set; }

        [Inject]
        private ISnackbar Snackbar { get; set; } = default!;

        [Inject]
        private NavigationManager Navigation { get; set; } = default!;

        protected string SortOption = "custom";
        protected List<CountryModel> Countries { get; set; } = new();
        protected bool IsLoading { get; set; } = true;
        protected string ErrorMessage { get; set; } = string.Empty;

        protected override async Task OnInitializedAsync()
        {
            await LoadCountriesAsync();
        }
        
        protected override async Task OnParametersSetAsync()
        {
            // This runs every time the parameters change (including when the component first loads)
            // If ID parameter is provided, load the country details
            if (Id.HasValue)
            {
                await LoadCountryDetails(Id.Value);
            }
            else
            {
                // If no ID is provided, show the list view
                showingDetails = false;
                selectedCountry = null;
            }
        }

        private async Task LoadCountriesAsync()
        {
            IsLoading = true;
            Countries.Clear();
            ErrorMessage = string.Empty;

            try
            {   
                // Fetch data from our API
                var apiUrl = "http://localhost:5200/api/country"; // Using our minimal API
                var result = await Http.GetFromJsonAsync<List<CountryModel>>(apiUrl);
                
                if (result != null)
                {
                    Countries = result;
                }
            }
            catch (Exception ex)
            {
                ErrorMessage = $"Error: {ex.Message}";
                // Fallback to mock data
                Countries = new List<CountryModel>
                {
                    new CountryModel { Id = 1, Name = "United States (Mock)", Verified = true },
                    new CountryModel { Id = 2, Name = "Canada (Mock)", Verified = true },
                    new CountryModel { Id = 3, Name = "Mexico (Mock)", Verified = true },
                    new CountryModel { Id = 4, Name = "United Kingdom (Mock)", Verified = true }
                };

                Snackbar.Add("Using mock data because API is not available", Severity.Warning);
                
                // If ID parameter is provided, try to find in mock data
                if (Id.HasValue)
                {
                    var mockCountry = Countries.FirstOrDefault(c => c.Id == Id.Value);
                    if (mockCountry != null)
                    {
                        selectedCountry = mockCountry;
                        showingDetails = true;
                    }
                }
            }
            finally
            {
                IsLoading = false;
                StateHasChanged();
            }
        }

        protected void AddNew()
    {
        newCountryName = string.Empty;
        dialogVisible = true;
        StateHasChanged();
    }
    
    protected void CancelAddCountry()
    {
        dialogVisible = false;
        StateHasChanged();
    }
    
    protected async Task SaveCountryAsync()
    {
        if (string.IsNullOrWhiteSpace(newCountryName))
        {
            Snackbar.Add("Country name cannot be empty", Severity.Warning);
            return;
        }
        
        await CreateCountryAsync(newCountryName);
        dialogVisible = false;
    }
    

        
        private async Task CreateCountryAsync(string countryName)
    {
        try
        {
            IsLoading = true;
            
            // Create request object
            var countryRequest = new
            {
                Name = countryName,
                Verified = true // Default to verified
            };
            
            // Send POST request to API
            var response = await Http.PostAsJsonAsync("api/country", countryRequest);
            
            if (response.IsSuccessStatusCode)
            {
                // Parse the response to get the new country
                var newCountry = await response.Content.ReadFromJsonAsync<CountryModel>();
                
                if (newCountry != null)
                {
                    // Add to the list if not already loaded from the API
                    if (Countries.All(c => c.Id != newCountry.Id))
                    {
                        Countries.Add(newCountry);
                    }
                    
                    Snackbar.Add($"Country '{countryName}' added successfully", Severity.Success);
                }
                else
                {
                    // If response couldn't be parsed, reload the entire list
                    await LoadCountriesAsync();
                    Snackbar.Add($"Country '{countryName}' added successfully", Severity.Success);
                }
            }
            else
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                Snackbar.Add($"Error adding country: {errorContent}", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Error adding country: {ex.Message}", Severity.Error);
        }
        finally
        {
            IsLoading = false;
        }
    }

        protected void Edit(CountryModel country)
        {
            // Stub: handle edit
        }

        protected void BackToList()
        {
            // Navigate back to the list URL
            Navigation.NavigateTo("/country");
        }

        protected async Task LoadCountryDetails(int id)
    {
        try
        {
            isLoadingDetails = true;
            
            // Fetch the detailed country data
            var apiUrl = $"http://localhost:5200/api/country/{id}";
            selectedCountry = await Http.GetFromJsonAsync<CountryModel>(apiUrl);
            
            // Show details view
            showingDetails = true;
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Error loading details: {ex.Message}", Severity.Error);
        }
        finally
        {
            isLoadingDetails = false;
        }
    }
    
    protected void ViewDetails(CountryModel country)
    {
        // Navigate to the country details URL
        Navigation.NavigateTo($"/country/{country.Id}");
    }   

        public class ConfigSettings
        {
            public ConnectionStringsConfig? ConnectionStrings { get; set; }
        }

        public class ConnectionStringsConfig
        {
            public string? DefaultConnection { get; set; }
        }
    }
}
