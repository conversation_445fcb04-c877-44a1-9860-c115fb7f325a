(function () {
    'use strict';

    // Create the main Angular module
    angular.module('mvcAngApp', [])
        .config(['$httpProvider', function ($httpProvider) {
            // Configure HTTP provider for CSRF protection if needed
            $httpProvider.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest';
        }])
        .run(['$rootScope', function ($rootScope) {
            // Global application initialization
            $rootScope.appName = 'MVC Angular 1.x App';
            console.log('Angular app initialized successfully!');
        }]);

})();
