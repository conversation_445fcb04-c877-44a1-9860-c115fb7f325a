/*
 * Bootstrap v3.4.1 - Placeholder CSS
 * This is a minimal placeholder file. In a real project, you would install Bootstrap via NuGet:
 * Install-Package bootstrap
 */

/* Basic Bootstrap-like styles for demonstration */
.container {
    max-width: 1170px;
    margin: 0 auto;
    padding: 0 15px;
}

.row {
    margin: 0 -15px;
}

.col-md-6, .col-md-12 {
    float: left;
    padding: 0 15px;
    position: relative;
}

.col-md-6 {
    width: 50%;
}

.col-md-12 {
    width: 100%;
}

.clearfix:after {
    content: "";
    display: table;
    clear: both;
}

/* Navigation */
.navbar {
    background-color: #222;
    border: 1px solid #080808;
    min-height: 50px;
    margin-bottom: 20px;
}

.navbar-inverse {
    background-color: #222;
    border-color: #080808;
}

.navbar-fixed-top {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1030;
}

.navbar-brand {
    color: #9d9d9d !important;
    text-decoration: none;
    font-size: 18px;
    line-height: 20px;
    padding: 15px;
    display: block;
    float: left;
}

.navbar-nav {
    list-style: none;
    margin: 0;
    padding: 0;
    float: left;
}

.navbar-nav li {
    float: left;
}

.navbar-nav li a {
    color: #9d9d9d;
    text-decoration: none;
    padding: 15px;
    display: block;
}

.navbar-nav li a:hover {
    color: #fff;
    background-color: transparent;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 6px 12px;
    margin-bottom: 0;
    font-size: 14px;
    font-weight: normal;
    line-height: 1.42857143;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    cursor: pointer;
    border: 1px solid transparent;
    border-radius: 4px;
    text-decoration: none;
}

.btn-primary {
    color: #fff;
    background-color: #337ab7;
    border-color: #2e6da4;
}

.btn-success {
    color: #fff;
    background-color: #5cb85c;
    border-color: #4cae4c;
}

.btn-info {
    color: #fff;
    background-color: #5bc0de;
    border-color: #46b8da;
}

.btn-lg {
    padding: 10px 16px;
    font-size: 18px;
    line-height: 1.3333333;
    border-radius: 6px;
}

/* Forms */
.form-control {
    display: block;
    width: 100%;
    height: 34px;
    padding: 6px 12px;
    font-size: 14px;
    line-height: 1.42857143;
    color: #555;
    background-color: #fff;
    border: 1px solid #ccc;
    border-radius: 4px;
    box-sizing: border-box;
}

.form-group {
    margin-bottom: 15px;
}

.form-horizontal .control-label {
    text-align: right;
    margin-bottom: 0;
    padding-top: 7px;
}

/* Alerts */
.alert {
    padding: 15px;
    margin-bottom: 20px;
    border: 1px solid transparent;
    border-radius: 4px;
}

.alert-success {
    color: #3c763d;
    background-color: #dff0d8;
    border-color: #d6e9c6;
}

.alert-danger {
    color: #a94442;
    background-color: #f2dede;
    border-color: #ebccd1;
}

/* Panels */
.panel {
    margin-bottom: 20px;
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.panel-default {
    border-color: #ddd;
}

.panel-heading {
    padding: 10px 15px;
    border-bottom: 1px solid #ddd;
    background-color: #f5f5f5;
    border-radius: 3px 3px 0 0;
}

.panel-body {
    padding: 15px;
}

/* Lists */
.list-group {
    padding-left: 0;
    margin-bottom: 20px;
    list-style: none;
}

.list-group-item {
    position: relative;
    display: block;
    padding: 10px 15px;
    margin-bottom: -1px;
    background-color: #fff;
    border: 1px solid #ddd;
}

/* Jumbotron */
.jumbotron {
    padding: 30px 15px;
    margin-bottom: 30px;
    color: inherit;
    background-color: #eee;
    border-radius: 6px;
}

.jumbotron h1 {
    font-size: 63px;
    margin: 0 0 20px;
}

.lead {
    margin-bottom: 20px;
    font-size: 21px;
    font-weight: 200;
    line-height: 1.4;
}

/* Utilities */
.help-block {
    display: block;
    margin-top: 5px;
    margin-bottom: 10px;
    color: #737373;
}

/* Icons placeholder */
.glyphicon {
    position: relative;
    top: 1px;
    display: inline-block;
    font-family: 'Glyphicons Halflings';
    font-style: normal;
    font-weight: normal;
    line-height: 1;
}

.glyphicon-refresh:before {
    content: "↻";
}
