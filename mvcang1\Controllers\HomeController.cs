using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using System.Net.Http;
using System.Threading.Tasks;
using System.IO;

namespace MvcAng1.Controllers
{
    public class HomeController : Controller
    {
        public ActionResult Index()
        {
            return View();
        }

        public ActionResult About()
        {
            ViewBag.Message = "Your application description page.";
            return View();
        }

        public ActionResult Contact()
        {
            ViewBag.Message = "Your contact page.";
            return View();
        }

        // API endpoint for Angular to consume
        public JsonResult GetData()
        {
            var data = new
            {
                message = "Hello from MVC Controller!",
                timestamp = DateTime.Now,
                items = new[]
                {
                    new { id = 1, name = "Item 1", description = "First item from server" },
                    new { id = 2, name = "Item 2", description = "Second item from server" },
                    new { id = 3, name = "Item 3", description = "Third item from server" }
                }
            };
            return Json(data, JsonRequestBehavior.AllowGet);
        }

        [HttpPost]
        public JsonResult SaveData(string name, string description)
        {
            // In a real application, you would save to database
            var result = new
            {
                success = true,
                message = $"Data saved successfully: {name} - {description}",
                timestamp = DateTime.Now
            };
            return Json(result);
        }

        // Type-ahead search endpoint that calls ASPX page
        public async Task<JsonResult> SearchTypeAhead(string search = "", string type = "countries")
        {
            try
            {
                // Build the URL to call the ASPX page
                string baseUrl = Request.Url.GetLeftPart(UriPartial.Authority);
                string aspxUrl = $"{baseUrl}/DataProvider.aspx?search={HttpUtility.UrlEncode(search)}&type={HttpUtility.UrlEncode(type)}";

                // Call the ASPX page
                using (var client = new HttpClient())
                {
                    var response = await client.GetAsync(aspxUrl);
                    if (response.IsSuccessStatusCode)
                    {
                        var content = await response.Content.ReadAsStringAsync();

                        // Return the JSON response from ASPX page
                        return Json(new
                        {
                            success = true,
                            aspxResponse = content,
                            calledUrl = aspxUrl,
                            timestamp = DateTime.Now,
                            source = "MVC Controller"
                        }, JsonRequestBehavior.AllowGet);
                    }
                    else
                    {
                        return Json(new
                        {
                            success = false,
                            error = $"ASPX call failed with status: {response.StatusCode}",
                            timestamp = DateTime.Now,
                            source = "MVC Controller"
                        }, JsonRequestBehavior.AllowGet);
                    }
                }
            }
            catch (Exception ex)
            {
                return Json(new
                {
                    success = false,
                    error = ex.Message,
                    timestamp = DateTime.Now,
                    source = "MVC Controller"
                }, JsonRequestBehavior.AllowGet);
            }
        }

        // Alternative synchronous version using WebRequest
        public JsonResult SearchTypeAheadSync(string search = "", string type = "countries")
        {
            try
            {
                // Build the URL to call the ASPX page
                string baseUrl = Request.Url.GetLeftPart(UriPartial.Authority);
                string aspxUrl = $"{baseUrl}/DataProvider.aspx?search={HttpUtility.UrlEncode(search)}&type={HttpUtility.UrlEncode(type)}";

                // Call the ASPX page using WebRequest
                var request = System.Net.WebRequest.Create(aspxUrl);
                request.Method = "GET";

                using (var response = request.GetResponse())
                using (var stream = response.GetResponseStream())
                using (var reader = new StreamReader(stream))
                {
                    var content = reader.ReadToEnd();

                    return Json(new
                    {
                        success = true,
                        aspxResponse = content,
                        calledUrl = aspxUrl,
                        timestamp = DateTime.Now,
                        source = "MVC Controller (Sync)"
                    }, JsonRequestBehavior.AllowGet);
                }
            }
            catch (Exception ex)
            {
                return Json(new
                {
                    success = false,
                    error = ex.Message,
                    timestamp = DateTime.Now,
                    source = "MVC Controller (Sync)"
                }, JsonRequestBehavior.AllowGet);
            }
        }
    }
}
