(function () {
    'use strict';

    angular
        .module('mvcAngApp')
        .factory('TypeAheadService', TypeAheadService);

    TypeAheadService.$inject = ['$http', '$q', '$timeout', 'TypeAheadModels'];

    function TypeAheadService($http, $q, $timeout, TypeAheadModels) {
        
        var service = {
            search: search,
            searchWithDebounce: searchWithDebounce,
            getDataTypes: getDataTypes,
            createConfig: createConfig,
            createState: createState
        };

        var pendingRequests = {};
        var debouncePromises = {};

        return service;

        function search(searchTerm, dataType) {
            var deferred = $q.defer();
            
            // Cancel any pending request for this data type
            if (pendingRequests[dataType]) {
                pendingRequests[dataType].resolve('cancelled');
            }
            
            pendingRequests[dataType] = deferred;

            var params = {
                search: searchTerm || '',
                type: dataType || 'countries'
            };

            $http.get('/Home/SearchTypeAheadSync', { params: params })
                .then(function (response) {
                    if (pendingRequests[dataType] === deferred) {
                        var searchResult = new TypeAheadModels.TypeAheadSearchResult(response.data);
                        deferred.resolve(searchResult);
                        delete pendingRequests[dataType];
                    }
                })
                .catch(function (error) {
                    if (pendingRequests[dataType] === deferred) {
                        var errorResult = new TypeAheadModels.TypeAheadSearchResult({
                            success: false,
                            error: error.data ? error.data.message || error.statusText : 'Network error',
                            searchTerm: searchTerm
                        });
                        deferred.resolve(errorResult);
                        delete pendingRequests[dataType];
                    }
                });

            return deferred.promise;
        }

        function searchWithDebounce(searchTerm, dataType, debounceMs) {
            var deferred = $q.defer();
            var key = dataType + '_' + searchTerm;
            
            debounceMs = debounceMs || 300;

            // Cancel previous debounce for this key
            if (debouncePromises[key]) {
                $timeout.cancel(debouncePromises[key]);
            }

            // Set up new debounced search
            debouncePromises[key] = $timeout(function() {
                search(searchTerm, dataType)
                    .then(function(result) {
                        deferred.resolve(result);
                        delete debouncePromises[key];
                    })
                    .catch(function(error) {
                        deferred.reject(error);
                        delete debouncePromises[key];
                    });
            }, debounceMs);

            return deferred.promise;
        }

        function getDataTypes() {
            return [
                { 
                    value: 'countries', 
                    label: 'Countries',
                    description: 'Search for countries by name or code'
                },
                { 
                    value: 'cities', 
                    label: 'Cities',
                    description: 'Search for cities by name or code'
                },
                { 
                    value: 'products', 
                    label: 'Products',
                    description: 'Search for products by name or code'
                },
                { 
                    value: 'users', 
                    label: 'Users',
                    description: 'Search for users by name or code'
                }
            ];
        }

        function createConfig(options) {
            return new TypeAheadModels.TypeAheadConfig(options);
        }

        function createState() {
            return new TypeAheadModels.TypeAheadState();
        }
    }

})();
