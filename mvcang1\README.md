# MVC 4.8 + Angular 1.x Application

This is a sample ASP.NET MVC 4.8 application integrated with Angular 1.x, demonstrating how to combine server-side MVC with client-side Angular functionality.

## Project Structure

```
mvcang1/
├── App_Data/                 # Application data
├── App_Start/               # Application startup configuration
│   ├── BundleConfig.cs     # Script and CSS bundling
│   ├── FilterConfig.cs     # Global filters
│   └── RouteConfig.cs      # URL routing
├── Controllers/            # MVC Controllers
│   └── HomeController.cs   # Main controller with API endpoints
├── Models/                 # Data models (empty for this demo)
├── Views/                  # Razor views
│   ├── Home/              # Home controller views
│   │   ├── Index.cshtml   # Main page with Angular integration
│   │   ├── About.cshtml   # About page
│   │   └── Contact.cshtml # Contact page
│   ├── Shared/            # Shared views
│   │   └── _Layout.cshtml # Main layout with Angular app declaration
│   ├── web.config         # Views configuration
│   └── _ViewStart.cshtml  # View start configuration
├── Scripts/               # JavaScript files
│   ├── app/              # Angular application
│   │   ├── controllers/  # Angular controllers
│   │   ├── services/     # Angular services
│   │   └── app.js        # Main Angular module
│   ├── angular.js        # Angular library (placeholder)
│   ├── jquery-3.4.1.js   # jQuery library (placeholder)
│   └── bootstrap.js      # Bootstrap JS (placeholder)
├── Content/              # CSS and static content
│   ├── bootstrap.css     # Bootstrap CSS (minimal version)
│   └── Site.css          # Custom application styles
├── Properties/           # Assembly information
├── bin/                  # Compiled assemblies
├── Web.config           # Main application configuration
├── Global.asax          # Global application events
├── packages.config      # NuGet packages configuration
└── MvcAng1.csproj      # Project file
```

## Features Demonstrated

### MVC 4.8 Features
- Controller actions returning both views and JSON
- Razor view engine with layout pages
- Bundle configuration for scripts and CSS
- RESTful API endpoints for Angular consumption

### Angular 1.x Features
- Angular module definition and bootstrapping
- Controllers with dependency injection
- Two-way data binding
- HTTP service for server communication
- Services for data management
- Form handling and validation

### Integration Features
- Server-side rendering with client-side enhancement
- AJAX communication between Angular and MVC
- Shared layout with Angular app initialization
- Bundle optimization for production

## Setup Instructions

### Prerequisites
- Visual Studio 2017 or later
- .NET Framework 4.8
- IIS Express (included with Visual Studio)

### Installation Steps

1. **Install NuGet Packages** (Replace placeholder files):
   ```
   Install-Package Microsoft.AspNet.Mvc -Version 5.2.7
   Install-Package jQuery -Version 3.4.1
   Install-Package bootstrap -Version 3.4.1
   Install-Package AngularJS.Core -Version 1.8.2
   Install-Package Modernizr -Version 2.8.3
   Install-Package Microsoft.AspNet.Web.Optimization
   Install-Package Newtonsoft.Json
   ```

2. **Replace Placeholder JavaScript Files**:
   - Download actual Angular 1.8.2 from CDN or NuGet
   - Download actual jQuery 3.4.1 from CDN or NuGet
   - Download actual Bootstrap 3.4.1 from CDN or NuGet

3. **Build the Project**:
   - Open the solution in Visual Studio
   - Build the project (Ctrl+Shift+B)

4. **Run the Application**:
   - Press F5 to start debugging
   - The application will open in your default browser

## Usage

### Home Page
- Demonstrates Angular controller integration
- "Load Data from Server" button fetches data via AJAX
- Form submission sends data to MVC controller
- Two-way data binding demonstration

### About Page
- Shows Angular controller in a different view
- Real-time clock update using Angular intervals

### API Endpoints
- `GET /Home/GetData` - Returns JSON data for Angular
- `POST /Home/SaveData` - Accepts form data from Angular

## Development Notes

### Adding New Angular Controllers
1. Create a new file in `Scripts/app/controllers/`
2. Follow the existing pattern with IIFE and dependency injection
3. Register the controller with the main Angular module

### Adding New MVC Actions
1. Add action methods to existing controllers or create new controllers
2. Return `JsonResult` for API endpoints consumed by Angular
3. Return `ActionResult` for traditional MVC views

### Bundling and Optimization
- Scripts and CSS are bundled via `BundleConfig.cs`
- Use `@Scripts.Render()` and `@Styles.Render()` in views
- Enable bundling optimization in `Web.config` for production

## Troubleshooting

### Common Issues
1. **Angular not loading**: Check that angular.js is properly referenced
2. **AJAX calls failing**: Verify controller action names and routes
3. **Bundling issues**: Check BundleConfig.cs for correct file paths
4. **CSS not loading**: Verify bootstrap.css and site.css are included

### Browser Console
- Check browser developer tools for JavaScript errors
- Angular controllers log activation messages to console
- AJAX requests and responses are logged for debugging

## Next Steps

To extend this application:
1. Add Entity Framework for data persistence
2. Implement authentication and authorization
3. Add more Angular features (routing, directives, filters)
4. Implement real-time features with SignalR
5. Add unit tests for both MVC and Angular components

## License

This is a sample application for demonstration purposes.
