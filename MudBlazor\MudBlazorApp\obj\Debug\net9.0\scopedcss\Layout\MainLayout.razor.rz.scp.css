.page[b-ju4q3hty1k] {
    position: relative;
    display: flex;
    flex-direction: column;
}

main[b-ju4q3hty1k] {
    flex: 1;
}

.sidebar[b-ju4q3hty1k] {
    background-image: linear-gradient(180deg, rgb(5, 39, 103) 0%, #3a0647 70%);
}

.top-row[b-ju4q3hty1k] {
    background-color: #f7f7f7;
    border-bottom: 1px solid #d6d5d5;
    justify-content: flex-end;
    height: 3.5rem;
    display: flex;
    align-items: center;
}

    .top-row[b-ju4q3hty1k]  a, .top-row[b-ju4q3hty1k]  .btn-link {
        white-space: nowrap;
        margin-left: 1.5rem;
        text-decoration: none;
    }

    .top-row[b-ju4q3hty1k]  a:hover, .top-row[b-ju4q3hty1k]  .btn-link:hover {
        text-decoration: underline;
    }

    .top-row[b-ju4q3hty1k]  a:first-child {
        overflow: hidden;
        text-overflow: ellipsis;
    }

@media (max-width: 640.98px) {
    .top-row[b-ju4q3hty1k] {
        justify-content: space-between;
    }

    .top-row[b-ju4q3hty1k]  a, .top-row[b-ju4q3hty1k]  .btn-link {
        margin-left: 0;
    }
}

@media (min-width: 641px) {
    .page[b-ju4q3hty1k] {
        flex-direction: row;
    }

    .sidebar[b-ju4q3hty1k] {
        width: 250px;
        height: 100vh;
        position: sticky;
        top: 0;
    }

    .top-row[b-ju4q3hty1k] {
        position: sticky;
        top: 0;
        z-index: 1;
    }

    .top-row.auth[b-ju4q3hty1k]  a:first-child {
        flex: 1;
        text-align: right;
        width: 0;
    }

    .top-row[b-ju4q3hty1k], article[b-ju4q3hty1k] {
        padding-left: 2rem !important;
        padding-right: 1.5rem !important;
    }
}
