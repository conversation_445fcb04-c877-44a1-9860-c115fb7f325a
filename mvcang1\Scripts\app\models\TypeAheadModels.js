(function () {
    'use strict';

    angular
        .module('mvcAngApp')
        .factory('TypeAheadModels', TypeAheadModels);

    function TypeAheadModels() {
        
        // TypeAhead Item Model
        function TypeAheadItem(data) {
            this.id = data.id || 0;
            this.name = data.name || '';
            this.code = data.code || '';
            this.displayText = data.name || '';
            this.originalData = data;
            
            // Additional properties based on data type
            if (data.region) this.region = data.region;
            if (data.country) this.country = data.country;
            if (data.category) this.category = data.category;
            if (data.department) this.department = data.department;
            if (data.email) this.email = data.email;
            if (data.population) this.population = data.population;
            if (data.price) this.price = data.price;
        }

        TypeAheadItem.prototype.getDisplayText = function() {
            return this.displayText;
        };

        TypeAheadItem.prototype.getSubText = function() {
            if (this.region) return this.region;
            if (this.country) return this.country;
            if (this.category) return this.category;
            if (this.department) return this.department;
            return this.code;
        };

        TypeAheadItem.prototype.toString = function() {
            return this.displayText;
        };

        // TypeAhead Configuration Model
        function TypeAheadConfig(options) {
            options = options || {};
            
            this.dataType = options.dataType || 'countries';
            this.placeholder = options.placeholder || 'Start typing to search...';
            this.minLength = options.minLength || 2;
            this.maxResults = options.maxResults || 10;
            this.debounceMs = options.debounceMs || 300;
            this.showDropdownOnFocus = options.showDropdownOnFocus || false;
            this.allowCustomValues = options.allowCustomValues || false;
            this.clearOnSelect = options.clearOnSelect || false;
            this.disabled = options.disabled || false;
        }

        // TypeAhead Search Result Model
        function TypeAheadSearchResult(data) {
            this.success = data.success || false;
            this.items = [];
            this.searchTerm = data.searchTerm || '';
            this.timestamp = data.timestamp || new Date();
            this.source = data.source || '';
            this.error = data.error || null;
            
            // Parse items if successful
            if (this.success && data.aspxResponse) {
                try {
                    var aspxData = JSON.parse(data.aspxResponse);
                    if (aspxData.success && aspxData.data) {
                        this.items = aspxData.data.map(function(item) {
                            return new TypeAheadItem(item);
                        });
                        this.aspxSource = aspxData.source;
                        this.aspxTimestamp = aspxData.timestamp;
                    }
                } catch (e) {
                    this.success = false;
                    this.error = 'Failed to parse ASPX response: ' + e.message;
                }
            }
        }

        TypeAheadSearchResult.prototype.hasResults = function() {
            return this.items && this.items.length > 0;
        };

        TypeAheadSearchResult.prototype.getResultCount = function() {
            return this.items ? this.items.length : 0;
        };

        // TypeAhead State Model
        function TypeAheadState() {
            this.isLoading = false;
            this.isOpen = false;
            this.searchTerm = '';
            this.selectedItem = null;
            this.highlightedIndex = -1;
            this.lastSearchResult = null;
            this.error = null;
        }

        TypeAheadState.prototype.reset = function() {
            this.isLoading = false;
            this.isOpen = false;
            this.searchTerm = '';
            this.selectedItem = null;
            this.highlightedIndex = -1;
            this.lastSearchResult = null;
            this.error = null;
        };

        TypeAheadState.prototype.selectItem = function(item) {
            this.selectedItem = item;
            this.searchTerm = item.getDisplayText();
            this.isOpen = false;
            this.highlightedIndex = -1;
        };

        TypeAheadState.prototype.highlightNext = function() {
            if (this.lastSearchResult && this.lastSearchResult.hasResults()) {
                this.highlightedIndex = Math.min(
                    this.highlightedIndex + 1, 
                    this.lastSearchResult.getResultCount() - 1
                );
            }
        };

        TypeAheadState.prototype.highlightPrevious = function() {
            this.highlightedIndex = Math.max(this.highlightedIndex - 1, -1);
        };

        TypeAheadState.prototype.selectHighlighted = function() {
            if (this.highlightedIndex >= 0 && 
                this.lastSearchResult && 
                this.lastSearchResult.hasResults()) {
                var item = this.lastSearchResult.items[this.highlightedIndex];
                this.selectItem(item);
                return item;
            }
            return null;
        };

        // Public API
        return {
            TypeAheadItem: TypeAheadItem,
            TypeAheadConfig: TypeAheadConfig,
            TypeAheadSearchResult: TypeAheadSearchResult,
            TypeAheadState: TypeAheadState
        };
    }

})();
