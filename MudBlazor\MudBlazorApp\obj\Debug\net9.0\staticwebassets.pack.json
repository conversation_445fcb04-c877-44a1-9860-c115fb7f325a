{"Files": [{"Id": "C:\\dev\\2025Stuff\\2025JulyProjects\\MudBlazor\\MudBlazorApp\\bin\\Debug\\net9.0\\wwwroot\\_framework\\blazor.webassembly.js", "PackagePath": "staticwebassets\\_framework\\blazor.webassembly.js"}, {"Id": "C:\\dev\\2025Stuff\\2025JulyProjects\\MudBlazor\\MudBlazorApp\\obj\\Debug\\net9.0\\scopedcss\\bundle\\MudBlazorApp.styles.css", "PackagePath": "staticwebassets\\MudBlazorApp.styles.css"}, {"Id": "C:\\dev\\2025Stuff\\2025JulyProjects\\MudBlazor\\MudBlazorApp\\wwwroot\\appsettings.json", "PackagePath": "staticwebassets\\appsettings.json"}, {"Id": "C:\\dev\\2025Stuff\\2025JulyProjects\\MudBlazor\\MudBlazorApp\\wwwroot\\css\\app.css", "PackagePath": "staticwebassets\\css\\app.css"}, {"Id": "C:\\dev\\2025Stuff\\2025JulyProjects\\MudBlazor\\MudBlazorApp\\wwwroot\\favicon.png", "PackagePath": "staticwebassets\\favicon.png"}, {"Id": "C:\\dev\\2025Stuff\\2025JulyProjects\\MudBlazor\\MudBlazorApp\\wwwroot\\icon-192.png", "PackagePath": "staticwebassets\\icon-192.png"}, {"Id": "C:\\dev\\2025Stuff\\2025JulyProjects\\MudBlazor\\MudBlazorApp\\wwwroot\\index.html", "PackagePath": "staticwebassets\\index.html"}, {"Id": "C:\\dev\\2025Stuff\\2025JulyProjects\\MudBlazor\\MudBlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css"}, {"Id": "C:\\dev\\2025Stuff\\2025JulyProjects\\MudBlazor\\MudBlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map"}, {"Id": "C:\\dev\\2025Stuff\\2025JulyProjects\\MudBlazor\\MudBlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css"}, {"Id": "C:\\dev\\2025Stuff\\2025JulyProjects\\MudBlazor\\MudBlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map"}, {"Id": "C:\\dev\\2025Stuff\\2025JulyProjects\\MudBlazor\\MudBlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css"}, {"Id": "C:\\dev\\2025Stuff\\2025JulyProjects\\MudBlazor\\MudBlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map"}, {"Id": "C:\\dev\\2025Stuff\\2025JulyProjects\\MudBlazor\\MudBlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css"}, {"Id": "C:\\dev\\2025Stuff\\2025JulyProjects\\MudBlazor\\MudBlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map"}, {"Id": "C:\\dev\\2025Stuff\\2025JulyProjects\\MudBlazor\\MudBlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css"}, {"Id": "C:\\dev\\2025Stuff\\2025JulyProjects\\MudBlazor\\MudBlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map"}, {"Id": "C:\\dev\\2025Stuff\\2025JulyProjects\\MudBlazor\\MudBlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css"}, {"Id": "C:\\dev\\2025Stuff\\2025JulyProjects\\MudBlazor\\MudBlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map"}, {"Id": "C:\\dev\\2025Stuff\\2025JulyProjects\\MudBlazor\\MudBlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css"}, {"Id": "C:\\dev\\2025Stuff\\2025JulyProjects\\MudBlazor\\MudBlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map"}, {"Id": "C:\\dev\\2025Stuff\\2025JulyProjects\\MudBlazor\\MudBlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css"}, {"Id": "C:\\dev\\2025Stuff\\2025JulyProjects\\MudBlazor\\MudBlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map"}, {"Id": "C:\\dev\\2025Stuff\\2025JulyProjects\\MudBlazor\\MudBlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css"}, {"Id": "C:\\dev\\2025Stuff\\2025JulyProjects\\MudBlazor\\MudBlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map"}, {"Id": "C:\\dev\\2025Stuff\\2025JulyProjects\\MudBlazor\\MudBlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css"}, {"Id": "C:\\dev\\2025Stuff\\2025JulyProjects\\MudBlazor\\MudBlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map"}, {"Id": "C:\\dev\\2025Stuff\\2025JulyProjects\\MudBlazor\\MudBlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css"}, {"Id": "C:\\dev\\2025Stuff\\2025JulyProjects\\MudBlazor\\MudBlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map"}, {"Id": "C:\\dev\\2025Stuff\\2025JulyProjects\\MudBlazor\\MudBlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css"}, {"Id": "C:\\dev\\2025Stuff\\2025JulyProjects\\MudBlazor\\MudBlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map"}, {"Id": "C:\\dev\\2025Stuff\\2025JulyProjects\\MudBlazor\\MudBlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap.css"}, {"Id": "C:\\dev\\2025Stuff\\2025JulyProjects\\MudBlazor\\MudBlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap.css.map"}, {"Id": "C:\\dev\\2025Stuff\\2025JulyProjects\\MudBlazor\\MudBlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap.min.css"}, {"Id": "C:\\dev\\2025Stuff\\2025JulyProjects\\MudBlazor\\MudBlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map"}, {"Id": "C:\\dev\\2025Stuff\\2025JulyProjects\\MudBlazor\\MudBlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css"}, {"Id": "C:\\dev\\2025Stuff\\2025JulyProjects\\MudBlazor\\MudBlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map"}, {"Id": "C:\\dev\\2025Stuff\\2025JulyProjects\\MudBlazor\\MudBlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css"}, {"Id": "C:\\dev\\2025Stuff\\2025JulyProjects\\MudBlazor\\MudBlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map"}, {"Id": "C:\\dev\\2025Stuff\\2025JulyProjects\\MudBlazor\\MudBlazorApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js"}, {"Id": "C:\\dev\\2025Stuff\\2025JulyProjects\\MudBlazor\\MudBlazorApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map"}, {"Id": "C:\\dev\\2025Stuff\\2025JulyProjects\\MudBlazor\\MudBlazorApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js"}, {"Id": "C:\\dev\\2025Stuff\\2025JulyProjects\\MudBlazor\\MudBlazorApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map"}, {"Id": "C:\\dev\\2025Stuff\\2025JulyProjects\\MudBlazor\\MudBlazorApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js"}, {"Id": "C:\\dev\\2025Stuff\\2025JulyProjects\\MudBlazor\\MudBlazorApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map"}, {"Id": "C:\\dev\\2025Stuff\\2025JulyProjects\\MudBlazor\\MudBlazorApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js"}, {"Id": "C:\\dev\\2025Stuff\\2025JulyProjects\\MudBlazor\\MudBlazorApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map"}, {"Id": "C:\\dev\\2025Stuff\\2025JulyProjects\\MudBlazor\\MudBlazorApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.js"}, {"Id": "C:\\dev\\2025Stuff\\2025JulyProjects\\MudBlazor\\MudBlazorApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.js.map"}, {"Id": "C:\\dev\\2025Stuff\\2025JulyProjects\\MudBlazor\\MudBlazorApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.min.js"}, {"Id": "C:\\dev\\2025Stuff\\2025JulyProjects\\MudBlazor\\MudBlazorApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map"}, {"Id": "C:\\dev\\2025Stuff\\2025JulyProjects\\MudBlazor\\MudBlazorApp\\wwwroot\\sample-data\\weather.json", "PackagePath": "staticwebassets\\sample-data\\weather.json"}, {"Id": "obj\\Debug\\net9.0\\staticwebassets\\msbuild.MudBlazorApp.Microsoft.AspNetCore.StaticWebAssetEndpoints.props", "PackagePath": "build\\Microsoft.AspNetCore.StaticWebAssetEndpoints.props"}, {"Id": "obj\\Debug\\net9.0\\staticwebassets\\msbuild.MudBlazorApp.Microsoft.AspNetCore.StaticWebAssets.props", "PackagePath": "build\\Microsoft.AspNetCore.StaticWebAssets.props"}, {"Id": "obj\\Debug\\net9.0\\staticwebassets\\msbuild.build.MudBlazorApp.props", "PackagePath": "build\\MudBlazorApp.props"}, {"Id": "obj\\Debug\\net9.0\\staticwebassets\\msbuild.buildMultiTargeting.MudBlazorApp.props", "PackagePath": "buildMultiTargeting\\MudBlazorApp.props"}, {"Id": "obj\\Debug\\net9.0\\staticwebassets\\msbuild.buildTransitive.MudBlazorApp.props", "PackagePath": "buildTransitive\\MudBlazorApp.props"}], "ElementsToRemove": []}