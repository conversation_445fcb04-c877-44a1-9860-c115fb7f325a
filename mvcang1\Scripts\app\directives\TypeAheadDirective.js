(function () {
    'use strict';

    angular
        .module('mvcAngApp')
        .directive('typeAhead', typeAhead);

    typeAhead.$inject = ['TypeAheadService', '$timeout', '$document'];

    function typeAhead(TypeAheadService, $timeout, $document) {
        return {
            restrict: 'E',
            scope: {
                config: '=',
                onSelect: '&',
                ngModel: '='
            },
            template: getTemplate(),
            link: linkFunction
        };

        function getTemplate() {
            return [
                '<div class="type-ahead-container">',
                '  <div class="input-group">',
                '    <input type="text" class="form-control type-ahead-input"',
                '           ng-model="searchTerm"',
                '           ng-keydown="handleKeyDown($event)"',
                '           ng-focus="handleFocus()"',
                '           ng-blur="handleBlur()"',
                '           placeholder="{{config.placeholder}}"',
                '           ng-disabled="config.disabled">',
                '    <span class="input-group-addon" ng-show="state.isLoading">',
                '      <i class="glyphicon glyphicon-refresh"></i>',
                '    </span>',
                '  </div>',
                '  <div class="type-ahead-dropdown" ng-show="state.isOpen && (state.lastSearchResult.hasResults() || state.error)">',
                '    <div class="type-ahead-error" ng-show="state.error">',
                '      <i class="glyphicon glyphicon-exclamation-sign"></i> {{state.error}}',
                '    </div>',
                '    <ul class="type-ahead-results" ng-show="state.lastSearchResult.hasResults()">',
                '      <li ng-repeat="item in state.lastSearchResult.items track by item.id"',
                '          class="type-ahead-result-item"',
                '          ng-class="{\'highlighted\': $index === state.highlightedIndex}"',
                '          ng-click="selectItem(item)"',
                '          ng-mouseenter="highlightItem($index)">',
                '        <div class="result-main">{{item.getDisplayText()}}</div>',
                '        <div class="result-sub" ng-show="item.getSubText()">{{item.getSubText()}}</div>',
                '      </li>',
                '    </ul>',
                '    <div class="type-ahead-footer" ng-show="state.lastSearchResult.hasResults()">',
                '      <small class="text-muted">{{state.lastSearchResult.getResultCount()}} results from {{state.lastSearchResult.aspxSource}}</small>',
                '    </div>',
                '  </div>',
                '</div>'
            ].join('');
        }

        function linkFunction(scope, element, attrs) {
            // Initialize
            scope.config = scope.config || TypeAheadService.createConfig();
            scope.state = TypeAheadService.createState();
            scope.searchTerm = '';

            var inputElement = element.find('input');
            var dropdownElement = element.find('.type-ahead-dropdown');

            // Watch for changes in search term
            scope.$watch('searchTerm', function(newValue, oldValue) {
                if (newValue !== oldValue) {
                    handleSearchTermChange(newValue);
                }
            });

            // Watch for external model changes
            scope.$watch('ngModel', function(newValue) {
                if (newValue && newValue !== scope.searchTerm) {
                    scope.searchTerm = newValue;
                }
            });

            // Event handlers
            scope.handleKeyDown = handleKeyDown;
            scope.handleFocus = handleFocus;
            scope.handleBlur = handleBlur;
            scope.selectItem = selectItem;
            scope.highlightItem = highlightItem;

            // Click outside to close
            $document.on('click', function(event) {
                if (!element[0].contains(event.target)) {
                    scope.$apply(function() {
                        scope.state.isOpen = false;
                    });
                }
            });

            // Cleanup
            scope.$on('$destroy', function() {
                $document.off('click');
            });

            function handleSearchTermChange(searchTerm) {
                scope.state.searchTerm = searchTerm;
                scope.ngModel = searchTerm;

                if (!searchTerm || searchTerm.length < scope.config.minLength) {
                    scope.state.isOpen = false;
                    scope.state.lastSearchResult = null;
                    return;
                }

                scope.state.isLoading = true;
                scope.state.error = null;

                TypeAheadService.searchWithDebounce(searchTerm, scope.config.dataType, scope.config.debounceMs)
                    .then(function(result) {
                        scope.state.isLoading = false;
                        scope.state.lastSearchResult = result;
                        scope.state.highlightedIndex = -1;

                        if (result.success) {
                            scope.state.isOpen = result.hasResults();
                            scope.state.error = null;
                        } else {
                            scope.state.error = result.error;
                            scope.state.isOpen = true;
                        }
                    })
                    .catch(function(error) {
                        scope.state.isLoading = false;
                        scope.state.error = 'Search failed: ' + (error.message || error);
                        scope.state.isOpen = true;
                    });
            }

            function handleKeyDown(event) {
                switch (event.keyCode) {
                    case 40: // Down arrow
                        event.preventDefault();
                        scope.state.highlightNext();
                        break;
                    case 38: // Up arrow
                        event.preventDefault();
                        scope.state.highlightPrevious();
                        break;
                    case 13: // Enter
                        event.preventDefault();
                        var selectedItem = scope.state.selectHighlighted();
                        if (selectedItem) {
                            notifySelection(selectedItem);
                        }
                        break;
                    case 27: // Escape
                        scope.state.isOpen = false;
                        inputElement[0].blur();
                        break;
                }
            }

            function handleFocus() {
                if (scope.config.showDropdownOnFocus && scope.state.lastSearchResult && scope.state.lastSearchResult.hasResults()) {
                    scope.state.isOpen = true;
                }
            }

            function handleBlur() {
                // Delay closing to allow for item selection
                $timeout(function() {
                    scope.state.isOpen = false;
                }, 150);
            }

            function selectItem(item) {
                scope.state.selectItem(item);
                scope.searchTerm = item.getDisplayText();
                scope.ngModel = scope.config.clearOnSelect ? '' : item.getDisplayText();
                notifySelection(item);
            }

            function highlightItem(index) {
                scope.state.highlightedIndex = index;
            }

            function notifySelection(item) {
                if (scope.onSelect) {
                    scope.onSelect({ item: item });
                }
            }
        }
    }

})();
