{"format": 1, "restore": {"C:\\dev\\2025Stuff\\2025JulyProjects\\MudBlazor\\MudBlazorMinimalApi\\MudBlazorMinimalApi.csproj": {}}, "projects": {"C:\\dev\\2025Stuff\\2025JulyProjects\\MudBlazor\\MudBlazorMinimalApi\\MudBlazorMinimalApi.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\dev\\2025Stuff\\2025JulyProjects\\MudBlazor\\MudBlazorMinimalApi\\MudBlazorMinimalApi.csproj", "projectName": "MudBlazorMinimalApi", "projectPath": "C:\\dev\\2025Stuff\\2025JulyProjects\\MudBlazor\\MudBlazorMinimalApi\\MudBlazorMinimalApi.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\dev\\2025Stuff\\2025JulyProjects\\MudBlazor\\MudBlazorMinimalApi\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\dev\\2025Stuff\\NuGet.Config", "C:\\dev\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files\\dotnet\\library-packs": {}, "C:\\dev\\LocalNuGet": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.Data.SqlClient": {"target": "Package", "version": "[6.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201/PortableRuntimeIdentifierGraph.json"}}}}}