@{
    ViewBag.Title = "Home Page";
}

<div ng-controller="HomeController as vm">
    <div class="jumbotron">
        <h1>MVC 4.8 + Angular 1.x</h1>
        <p class="lead">This is a demonstration of ASP.NET MVC 4.8 integrated with Angular 1.x</p>
        <p><a href="#" class="btn btn-primary btn-lg" ng-click="vm.loadData()">Load Data from Server &raquo;</a></p>
    </div>

    <div class="row">
        <div class="col-md-6">
            <h2>Server Data</h2>
            <div ng-show="vm.loading">
                <p><i class="glyphicon glyphicon-refresh"></i> Loading...</p>
            </div>
            <div ng-show="vm.serverData && !vm.loading">
                <p><strong>Message:</strong> {{vm.serverData.message}}</p>
                <p><strong>Timestamp:</strong> {{vm.serverData.timestamp | date:'medium'}}</p>
                <h4>Items:</h4>
                <ul class="list-group">
                    <li class="list-group-item" ng-repeat="item in vm.serverData.items">
                        <strong>{{item.name}}</strong> - {{item.description}}
                    </li>
                </ul>
            </div>
        </div>
        <div class="col-md-6">
            <h2>Send Data to Server</h2>
            <form ng-submit="vm.saveData()" class="form-horizontal">
                <div class="form-group">
                    <label for="itemName" class="col-sm-3 control-label">Name:</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="itemName" ng-model="vm.newItem.name" placeholder="Enter item name" required>
                    </div>
                </div>
                <div class="form-group">
                    <label for="itemDescription" class="col-sm-3 control-label">Description:</label>
                    <div class="col-sm-9">
                        <textarea class="form-control" id="itemDescription" ng-model="vm.newItem.description" placeholder="Enter description" required></textarea>
                    </div>
                </div>
                <div class="form-group">
                    <div class="col-sm-offset-3 col-sm-9">
                        <button type="submit" class="btn btn-success" ng-disabled="vm.saving">
                            <span ng-show="vm.saving"><i class="glyphicon glyphicon-refresh"></i> Saving...</span>
                            <span ng-hide="vm.saving">Save Data</span>
                        </button>
                    </div>
                </div>
            </form>
            <div ng-show="vm.saveResult" class="alert" ng-class="{'alert-success': vm.saveResult.success, 'alert-danger': !vm.saveResult.success}">
                {{vm.saveResult.message}}
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <h2>Angular Features Demo</h2>
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4>Two-way Data Binding</h4>
                </div>
                <div class="panel-body">
                    <input type="text" class="form-control" ng-model="vm.bindingDemo" placeholder="Type something here...">
                    <p class="help-block">You typed: <strong>{{vm.bindingDemo}}</strong></p>
                </div>
            </div>
        </div>
    </div>

    <!-- Type-ahead Demo Section -->
    <div class="row" ng-controller="TypeAheadController as taVm">
        <div class="col-md-12">
            <h2>Type-ahead Dropdown Demo</h2>
            <p class="lead">This demonstrates Angular type-ahead functionality that calls MVC controller, which then calls ASPX page for data.</p>

            <div class="row">
                <div class="col-md-6">
                    <h4>Search Examples</h4>

                    <div class="form-group">
                        <label>Countries:</label>
                        <type-ahead config="taVm.countryConfig"
                                   ng-model="taVm.selectedCountry"
                                   on-select="taVm.onCountrySelect(item)">
                        </type-ahead>
                        <p class="help-block">Try typing: "United", "US", "Germany", "Asia"</p>
                    </div>

                    <div class="form-group">
                        <label>Cities:</label>
                        <type-ahead config="taVm.cityConfig"
                                   ng-model="taVm.selectedCity"
                                   on-select="taVm.onCitySelect(item)">
                        </type-ahead>
                        <p class="help-block">Try typing: "New", "London", "Tokyo", "Paris"</p>
                    </div>

                    <div class="form-group">
                        <label>Products:</label>
                        <type-ahead config="taVm.productConfig"
                                   ng-model="taVm.selectedProduct"
                                   on-select="taVm.onProductSelect(item)">
                        </type-ahead>
                        <p class="help-block">Try typing: "Laptop", "Coffee", "USB", "Electronics"</p>
                    </div>

                    <div class="form-group">
                        <label>Users:</label>
                        <type-ahead config="taVm.userConfig"
                                   ng-model="taVm.selectedUser"
                                   on-select="taVm.onUserSelect(item)">
                        </type-ahead>
                        <p class="help-block">Try typing: "John", "IT", "Sales", "@@example"</p>
                    </div>

                    <button class="btn btn-warning" ng-click="taVm.clearSelections()">Clear All Selections</button>
                </div>

                <div class="col-md-6">
                    <h4>Selection History</h4>
                    <div ng-show="taVm.selectedItems.length === 0" class="alert alert-info">
                        <i class="glyphicon glyphicon-info-sign"></i> No selections yet. Try searching and selecting items from the dropdowns.
                    </div>

                    <div ng-show="taVm.selectedItems.length > 0">
                        <ul class="list-group">
                            <li class="list-group-item" ng-repeat="selection in taVm.selectedItems track by $index">
                                <div class="row">
                                    <div class="col-sm-3">
                                        <span class="label label-primary">{{selection.type}}</span>
                                    </div>
                                    <div class="col-sm-9">
                                        <strong>{{selection.displayText}}</strong>
                                        <div ng-show="selection.subText" class="text-muted">{{selection.subText}}</div>
                                        <small class="text-muted">Selected at {{selection.timestamp | date:'medium'}}</small>
                                    </div>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-12">
                    <h4>Data Flow Architecture</h4>
                    <div class="panel panel-info">
                        <div class="panel-body">
                            <ol>
                                <li><strong>Angular Directive:</strong> User types in type-ahead input</li>
                                <li><strong>Angular Service:</strong> Debounces input and calls MVC endpoint</li>
                                <li><strong>MVC Controller:</strong> Receives request and calls ASPX page</li>
                                <li><strong>ASPX Page:</strong> Returns mock data based on search term and type</li>
                                <li><strong>MVC Controller:</strong> Processes ASPX response and returns to Angular</li>
                                <li><strong>Angular Directive:</strong> Displays results in dropdown</li>
                            </ol>
                            <p><strong>Endpoints:</strong></p>
                            <ul>
                                <li>Angular → <code>/Home/SearchTypeAheadSync</code> → <code>/DataProvider.aspx</code></li>
                                <li>Available data types: {{taVm.dataTypes.map(function(dt) { return dt.label; }).join(', ')}}</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
