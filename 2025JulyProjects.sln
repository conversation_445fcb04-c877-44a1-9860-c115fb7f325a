Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.5.2.0
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "MudBlazor", "MudBlazor", "{E4C154BC-C5BE-FA71-EB6D-8A24DA36BA1D}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "MudBlazorApp", "MudBlazor\MudBlazorApp\MudBlazorApp.csproj", "{D6922306-C1FC-D99E-3BA9-709F3BA91883}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{D6922306-C1FC-D99E-3BA9-709F3BA91883}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D6922306-C1FC-D99E-3BA9-709F3BA91883}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D6922306-C1FC-D99E-3BA9-709F3BA91883}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D6922306-C1FC-D99E-3BA9-709F3BA91883}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{D6922306-C1FC-D99E-3BA9-709F3BA91883} = {E4C154BC-C5BE-FA71-EB6D-8A24DA36BA1D}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {36D3D702-3E89-4B81-BB27-F7C0298AF6F1}
	EndGlobalSection
EndGlobal
